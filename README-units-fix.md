# Units Fix for Migrated Data

This document explains how to fix the units issue after the migration is complete.

## Background

In the old database, all items were set as PCE (piece) units. Now we want to properly set up units and parent-child relationships between items.

## Files

- `migrate_units.json`: Contains the data for items that need to be fixed
- `fix-units.js`: <PERSON><PERSON><PERSON> to fix the units issue
- `verify-units-fix.js`: <PERSON><PERSON><PERSON> to verify that the units fix was applied correctly

## How to Use

### 1. Run the Migration

First, run the migration script to migrate all the data from the old database to the new database:

```bash
node migrator.js
```

### 2. Fix the Units

After the migration is complete, run the units fix script:

```bash
node fix-units.js
```

This script will:

1. Process `sub_items_with_parent`:
   - Update items to be children of their parent items
   - Set the proper unit for each item
   - Remove the original item from the Items table
   - Update references in SalesOrderItems and SalesOrderReturnItems

2. Process `sub_items_without_parent`:
   - Add the unit under the specified parent_id
   - Set the conversion_factor and taxed_price
   - Update references in SalesOrderItems and SalesOrderReturnItems

### 3. Verify the Fix

After fixing the units, you can verify that the fix was applied correctly:

```bash
node verify-units-fix.js
```

This script will check:

1. For `sub_items_with_parent`:
   - The sub item has been removed from the Items table
   - The parent item has the unit
   - SalesOrderItems have been updated to use the parent item with the new unit
   - SalesOrderReturnItems have been updated to use the parent item with the new unit

2. For `sub_items_without_parent`:
   - The parent item has the unit
   - SalesOrderItems have been updated to use the new unit
   - SalesOrderReturnItems have been updated to use the new unit

## JSON File Structure

The `migrate_units.json` file has two main sections:

### 1. sub_items_with_parent

These are items that should be converted to be children of their parent items.

Example:
```json
{
  "parent_id": "1986",
  "parent_name": "ماسورة 3/4 إنش بارد",
  "parent_unit": "PCE",
  "item_id": "558",
  "name": "ماسورة 3/4 إنش بارد (متر)",
  "unit": "(متر)",
  "taxed_price": "3",
  "conversion_factor": 0.16666666666666666
}
```

### 2. sub_items_without_parent

These are items that should be sub-items but don't have a parent yet.

Example:
```json
{
  "parent_id": "664",
  "parent_name": "واير مقاس 10",
  "parent_unit": "PCE",
  "item_id": "664",
  "name": "واير مقاس 10 (بالمتر)",
  "unit": "(بالمتر)",
  "taxed_price": "3",
  "conversion_factor": 0.006666666666666667
}
```

## Notes

- The script uses transactions to ensure that all changes are applied atomically
- If an error occurs, the transaction will be rolled back
- The script will log all actions and errors to the console
