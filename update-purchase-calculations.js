/**
 * Update Purchase Calculations module
 * 
 * This module updates the PaidAmount and RemainingAmount calculations for purchase orders
 * after all transactions have been created.
 */

const { performance } = require('perf_hooks');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

/**
 * Update PaidAmount and RemainingAmount calculations for purchase orders
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function updatePurchaseCalculations(db, batchSize = 10000) {
  console.log('Updating PaidAmount and RemainingAmount calculations for purchase orders...');

  // Get total count of orders
  const ordersCount = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrders');
  console.log(`Found ${ordersCount.count} purchase orders to update.`);

  // Process in batches
  const orderBatchSize = Math.min(batchSize, ordersCount.count);
  const orderBatches = Math.ceil(ordersCount.count / orderBatchSize);

  for (let batch = 0; batch < orderBatches; batch++) {
    const offset = batch * orderBatchSize;
    console.log(`Processing purchase orders batch ${batch + 1}/${orderBatches} (offset: ${offset})...`);

    await run(db, `
      UPDATE PurchaseOrders
      SET
        RemainingAmount = (
          -- For suppliers: RemainingAmount = Sum(Credit of supplier) - Sum(Debit of supplier)
          -- Only consider transactions related to this specific order
          (SELECT COALESCE(SUM(Amount), 0) FROM Transactions 
           WHERE CreditAccountID = PurchaseOrders.SupplierID AND ReferenceID = PurchaseOrders.OrderID) -
          (SELECT COALESCE(SUM(Amount), 0) FROM Transactions 
           WHERE DebitAccountID = PurchaseOrders.SupplierID AND ReferenceID = PurchaseOrders.OrderID)
        ),
        PaidAmount = (
          -- For suppliers: PaidAmount = TotalAfterReturns - RemainingAmount
          TotalAfterReturns - (
            (SELECT COALESCE(SUM(Amount), 0) FROM Transactions 
             WHERE CreditAccountID = PurchaseOrders.SupplierID AND ReferenceID = PurchaseOrders.OrderID) -
            (SELECT COALESCE(SUM(Amount), 0) FROM Transactions 
             WHERE DebitAccountID = PurchaseOrders.SupplierID AND ReferenceID = PurchaseOrders.OrderID)
          )
        )
      WHERE rowid IN (
        SELECT rowid FROM PurchaseOrders
        ORDER BY rowid
        LIMIT ${orderBatchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('PaidAmount and RemainingAmount calculations updated successfully for purchase orders.');
}

/**
 * Main function to update purchase calculations
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function updatePurchaseOrderCalculations(db, startTime, callback) {
  try {
    console.log('Starting purchase order calculations update...');

    // Update PaidAmount and RemainingAmount calculations
    await updatePurchaseCalculations(db);

    const endTime = performance.now();
    console.log(`Purchase order calculations update completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error updating purchase order calculations: ${err.message}`);
    callback(err);
  }
}

module.exports = { updatePurchaseOrderCalculations };
