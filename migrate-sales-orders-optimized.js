/**
 * Sales Orders migration module - Copy Only Version
 *
 * This module simply copies data from old tables to new tables with no calculations:
 * - sales_orders → SalesOrders
 * - sales_order_change_additions → SalesOrderItems
 * - sales_order_changes → SalesOrderReturns (only those with returns)
 * - sales_order_change_returns → SalesOrderReturnItems
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Clear all related tables before migration
 * @param {object} db - Database connection
 */
async function clearTables(db) {
  console.log('Clearing related tables...');
  // Payments table has been removed, no need to clear it
  await run(db, "DELETE FROM SalesOrderReturnItems");
  await run(db, "DELETE FROM SalesOrderReturns");
  await run(db, "DELETE FROM SalesOrderItems");
  await run(db, "DELETE FROM SalesOrders");
  console.log('All related tables cleared successfully.');
}

/**
 * Create necessary mapping tables
 * @param {object} db - Database connection
 */
async function createMappingTables(db) {
  console.log('Creating mapping tables...');

  // Create temporary tables for mapping IDs
  await run(db, `
    CREATE TEMPORARY TABLE order_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  await run(db, `
    CREATE TEMPORARY TABLE return_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Create indexes on temporary tables
  await run(db, `CREATE INDEX idx_order_mapping_new_id ON order_mapping(new_id)`);
  await run(db, `CREATE INDEX idx_return_mapping_new_id ON return_mapping(new_id)`);

  console.log('Mapping tables created successfully.');
}

/**
 * Copy sales_orders to SalesOrders
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copySalesOrders(db, timestamp, batchSize = 10000) {
  console.log('Copying sales_orders to SalesOrders...');

  // Get total count of sales orders to migrate
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM old_db.sales_orders WHERE confirmed = 1');
  const totalOrders = countResult.count;
  console.log(`Found ${totalOrders} confirmed sales orders to copy.`);

  // Process in batches
  const batches = Math.ceil(totalOrders / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} orders each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales orders
    const orders = await all(db, `
      SELECT * FROM old_db.sales_orders
      WHERE confirmed = 1
      ORDER BY id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) {
      console.log(`No orders found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${orders.length} orders in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];
    let orderMappingValues = [];

    // Get customer IDs
    const customerIds = orders.map(order => order.customer_id).filter(id => id);
    let customerMap = {};

    if (customerIds.length > 0) {
      // Create a temporary customer mapping table if it doesn't exist
      await run(db, `
        CREATE TEMPORARY TABLE IF NOT EXISTS customer_mapping (
          old_id INTEGER PRIMARY KEY,
          new_id TEXT NOT NULL
        )
      `);

      // Check if the table is empty
      const countResult = await get(db, 'SELECT COUNT(*) as count FROM customer_mapping');

      if (countResult.count === 0) {
        console.log('Populating customer mapping table...');
        // For each customer in the old database, find the corresponding customer in the new database
        // This is a simplified approach - in a real scenario, you might need more complex matching logic
        for (const customerId of customerIds) {
          // Get customer from old database
          const oldCustomer = await get(db, `
            SELECT id, full_name, tax_number
            FROM old_db.customers
            WHERE id = ?
          `, [customerId]);

          if (oldCustomer) {
            // Look for matching customer in new database
            const newCustomer = await get(db, `
              SELECT CustomerID
              FROM Customers
              WHERE Name = ?
            `, [oldCustomer.full_name]);

            if (newCustomer) {
              // Insert into mapping table
              await run(db, `
                INSERT OR IGNORE INTO customer_mapping (old_id, new_id)
                VALUES (?, ?)
              `, [oldCustomer.id, newCustomer.CustomerID]);

              // Add to in-memory map
              customerMap[oldCustomer.id] = newCustomer.CustomerID;
            }
          }
        }
        console.log('Customer mapping table populated.');
      } else {
        // Get existing mappings
        const customerMappings = await all(db, `
          SELECT old_id, new_id
          FROM customer_mapping
          WHERE old_id IN (${customerIds.join(',')})
        `);

        for (const mapping of customerMappings) {
          customerMap[mapping.old_id] = mapping.new_id;
        }
      }
    }

    // Use C-UNKNOWN as the unknown customer ID
    const unknownCustomerId = 'C-UNKNOWN';

    for (const order of orders) {
      const orderId = uuidv4();
      const salesOrderNumber = `SO-${order.id.toString().padStart(6, '0')}`;
      const invoiceNumber = `INV-${order.id.toString().padStart(6, '0')}`;

      // Get customer UUID from mapping
      let customerId;
      if (order.customer_id && customerMap[order.customer_id]) {
        customerId = customerMap[order.customer_id];
      } else {
        customerId = unknownCustomerId;
      }

      // Check if customer has VAT number to determine business segment
      let hasVatNumber = false;

      if (customerId !== unknownCustomerId) {
        const customer = await get(db, `
          SELECT VATNumber FROM Customers WHERE CustomerID = ?
        `, [customerId]);

        hasVatNumber = customer && customer.VATNumber && customer.VATNumber.trim() !== '';
      }

      const businessSegment = hasVatNumber ? 'B2B' : 'B2C';

      insertValues.push(`(
        '${orderId}',
        '${salesOrderNumber}',
        '${invoiceNumber}',
        '${customerId}',
        '${order.transaction_date || timestamp}',
        '${businessSegment}',
        1,
        ${order.note ? `'${order.note.replace(/'/g, "''")}'` : 'NULL'},
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        '${invoiceNumber}',
        '${timestamp}',
        '${timestamp}',
        '${order.user || 'admin'}',
        '${order.user || 'admin'}'
      )`);

      orderMappingValues.push(`(${order.id}, '${orderId}')`);
    }

    // Insert sales orders
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO SalesOrders (
          OrderID, SalesOrderNumber, InvoiceNumber, CustomerID,
          OrderDate, BusinessSegment, Processed, Notes, InvoiceSubtotal,
          InvoiceDiscountAmount, InvoiceTaxableAmount, InvoiceVATAmount,
          InvoiceTotalAmount, TotalReturnAmount, TotalAfterReturns,
          PaidAmount, RemainingAmount, SalesItemsCount, ReturnsItemsCount,
          SalesEntriesCount, ReturnsEntriesCount, ReturnsCount,
          FinalInvoiceNumber, CreatedAt, UpdatedAt,
          CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    // Insert order mappings
    if (orderMappingValues.length > 0) {
      await run(db, `
        INSERT INTO order_mapping (old_id, new_id)
        VALUES ${orderMappingValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} sales orders.`);
  return totalProcessed;
}

/**
 * Copy sales_order_change_additions to SalesOrderItems
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {boolean} useNewCalculationMethod - Whether to use new unit-based calculation method
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copySalesOrderItems(db, timestamp, useNewCalculationMethod, batchSize = 10000) {
  console.log('Copying sales_order_change_additions to SalesOrderItems...');

  // Get total count of items to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.sales_order_change_additions a
    JOIN old_db.sales_order_changes c ON a.sales_order_change_id = c.id
    WHERE c.confirmed = 1
  `);
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} sales order items to copy.`);

  // Pre-fetch all item data to avoid individual lookups
  console.log('Pre-fetching item data...');
  const itemsData = await all(db, `
    SELECT i.ItemID, i.ItemNumber,
           COALESCE((SELECT name FROM old_db.items WHERE id = i.ItemNumber), 'Item ' || i.ItemNumber) as ItemName
    FROM Items i
  `);

  // Create a lookup map for quick access
  const itemsMap = {};
  for (const item of itemsData) {
    itemsMap[item.ItemNumber] = {
      ItemID: item.ItemID,
      ItemName: item.ItemName
    };
  }
  console.log(`Pre-fetched data for ${itemsData.length} items.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of items
    const items = await all(db, `
      SELECT
        a.*,
        c.user,
        om.new_id as order_id
      FROM old_db.sales_order_change_additions a
      JOIN old_db.sales_order_changes c ON a.sales_order_change_id = c.id
      JOIN order_mapping om ON a.sales_order_id = om.old_id
      WHERE c.confirmed = 1
      ORDER BY a.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];

    for (const item of items) {
      const itemId = uuidv4();
      const barcode = item.barcode || `ABC${item.item_id.toString().padStart(7, '0')}`;

      // Get item info from the pre-fetched map
      const itemInfo = itemsMap[item.item_id] || { ItemID: null, ItemName: `Item ${item.item_id}` };

      let unitTotalAmount, lineVATAmount, lineTaxableAmount, lineTotalAmount, lineSubtotal, lineDiscountAmount, discount;
      const price = parseFloat(item.unit_selling_price || 0);
      const quantity = item.quantity || 0;
      const taxRate = item.tax || 15;

      if (useNewCalculationMethod) {
        // NEW METHOD: Calculate using the exact formula provided
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_selling_price || 0)).toFixed(2);

        // Calculate unit-level values first
        const unitVAT = +((unitTotalAmount - (unitTotalAmount / (1 + (taxRate / 100)))).toFixed(2));
        const unitTaxable = +((unitTotalAmount / (1 + (taxRate / 100))).toFixed(2));

        // Calculate line-level values by multiplying unit values by quantity
        lineVATAmount = +(unitVAT * quantity).toFixed(2);
        lineTaxableAmount = +(unitTotalAmount * quantity - unitVAT * quantity).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineSubtotal = +(price * quantity).toFixed(2);
        lineDiscountAmount = +((price - unitTaxable) * quantity).toFixed(2);
        discount = price > 0 ? +((1 - (unitTaxable / price)) * 100).toFixed(2) : 0;
      } else {
        // OLD METHOD: Calculate based on taxed_discounted_unit_selling_price as unit price
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_selling_price || 0)).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineTaxableAmount = +((unitTotalAmount / (1 + taxRate / 100)) * quantity).toFixed(2);
        lineVATAmount = +((lineTaxableAmount * (taxRate / 100))).toFixed(2);
        lineSubtotal = +(lineTaxableAmount + lineVATAmount).toFixed(2);
        lineDiscountAmount = +(lineSubtotal - lineTaxableAmount).toFixed(2);
        discount = item.discount || 0;
      }

      insertValues.push(`(
        '${itemId}',
        '${item.order_id}',
        '${barcode}',
        ${itemInfo.ItemID ? `'${itemInfo.ItemID}'` : 'NULL'},
        '${(itemInfo.ItemName || `Item ${item.item_id}`).replace(/'/g, "''")}',
        'PCE',
        ${quantity},
        ${price.toFixed(2)},
        ${parseFloat(item.unit_purchase_price || 0).toFixed(2)},
        ${discount},
        ${taxRate},
        ${lineSubtotal}, ${lineDiscountAmount}, ${lineTaxableAmount}, ${lineVATAmount}, ${lineTotalAmount}, ${unitTotalAmount},
        ${item.item_id},
        'S',
        '${timestamp}',
        '${timestamp}',
        '${item.user || 'admin'}',
        '${item.user || 'admin'}'
      )`);
    }

    // Insert items
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO SalesOrderItems (
          OrderItemID, OrderID, Barcode, ItemID,
          ItemName, UnitID, Quantity, Price,
          UnitCost, Discount, TaxRate, LineSubtotal,
          LineDiscountAmount, LineTaxableAmount, LineVATAmount,
          LineTotalAmount, UnitTotalAmount, ItemNumber,
          TaxID, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} sales order items.`);
  return totalProcessed;
}

/**
 * Copy sales_order_changes to SalesOrderReturns (only those with returns)
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copySalesOrderReturns(db, timestamp, batchSize = 10000) {
  console.log('Copying sales_order_changes to SalesOrderReturns...');

  // Get total count of returns to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.sales_order_changes
    WHERE confirmed = 1 AND returned_taxed_discounted_selling_price > 0
  `);
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} sales order returns to copy.`);

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} returns each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of returns
    const returns = await all(db, `
      SELECT
        c.*,
        om.new_id as order_id
      FROM old_db.sales_order_changes c
      JOIN order_mapping om ON c.sales_order_id = om.old_id
      WHERE c.confirmed = 1 AND c.returned_taxed_discounted_selling_price > 0
      ORDER BY c.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) {
      console.log(`No returns found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${returns.length} returns in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];
    let returnMappingValues = [];

    for (const ret of returns) {
      const returnId = uuidv4();
      const returnNumber = `RET-${ret.id.toString().padStart(6, '0')}`;
      const invoiceNumber = `INV-${ret.sales_order_id.toString().padStart(6, '0')}`;
      const finalInvoiceNumber = returnNumber; // Use return number as final invoice number

      insertValues.push(`(
        '${returnId}',
        '${ret.order_id}',
        '${returnNumber}',
        '${ret.transaction_date || timestamp}',
        1,
        '${invoiceNumber}',
        0, 0, 0, 0, 0, 0, 0,
        '${finalInvoiceNumber}',
        '${timestamp}',
        '${timestamp}',
        '${ret.user || 'admin'}',
        '${ret.user || 'admin'}'
      )`);

      returnMappingValues.push(`(${ret.id}, '${returnId}')`);
    }

    // Insert returns
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO SalesOrderReturns (
          ReturnID, OrderID, ReturnNumber,
          ReturnDate, Processed, InvoiceNumber, InvoiceSubtotal,
          InvoiceDiscountAmount, InvoiceTaxableAmount, InvoiceVATAmount,
          InvoiceTotalAmount, ReturnItemsCount, ReturnEntriesCount,
          FinalInvoiceNumber, CreatedAt, UpdatedAt,
          CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    // Insert return mappings
    if (returnMappingValues.length > 0) {
      await run(db, `
        INSERT INTO return_mapping (old_id, new_id)
        VALUES ${returnMappingValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} sales order returns.`);
  return totalProcessed;
}

/**
 * Copy sales_order_change_returns to SalesOrderReturnItems
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {boolean} useNewCalculationMethod - Whether to use new unit-based calculation method
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copySalesOrderReturnItems(db, timestamp, useNewCalculationMethod, batchSize = 10000) {
  console.log('Copying sales_order_change_returns to SalesOrderReturnItems...');

  // Get total count of return items to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.sales_order_change_returns r
    JOIN old_db.sales_order_changes c ON r.sales_order_change_id = c.id
    WHERE r.confirmed = 1
  `);
  const totalReturnItems = countResult.count;
  console.log(`Found ${totalReturnItems} sales order return items to copy.`);

  // Pre-fetch all item data to avoid individual lookups
  console.log('Pre-fetching item data...');
  const itemsData = await all(db, `
    SELECT i.ItemID, i.ItemNumber,
           COALESCE((SELECT name FROM old_db.items WHERE id = i.ItemNumber), 'Item ' || i.ItemNumber) as ItemName
    FROM Items i
  `);

  // Create a lookup map for quick access
  const itemsMap = {};
  for (const item of itemsData) {
    itemsMap[item.ItemNumber] = {
      ItemID: item.ItemID,
      ItemName: item.ItemName
    };
  }
  console.log(`Pre-fetched data for ${itemsData.length} items.`);

  // Process in batches
  const batches = Math.ceil(totalReturnItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} return items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of return items
    const returnItems = await all(db, `
      SELECT
        r.*,
        c.user,
        om.new_id as order_id,
        rm.new_id as return_id
      FROM old_db.sales_order_change_returns r
      JOIN old_db.sales_order_changes c ON r.sales_order_change_id = c.id
      JOIN order_mapping om ON r.sales_order_id = om.old_id
      JOIN return_mapping rm ON r.sales_order_change_id = rm.old_id
      WHERE r.confirmed = 1
      ORDER BY r.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returnItems.length === 0) {
      console.log(`No return items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${returnItems.length} return items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];

    for (const item of returnItems) {
      const returnItemId = uuidv4();
      const barcode = item.barcode || `ABC${item.item_id.toString().padStart(7, '0')}`;

      // Get item info from the pre-fetched map
      const itemInfo = itemsMap[item.item_id] || { ItemID: null, ItemName: `Item ${item.item_id}` };

      let unitTotalAmount, lineVATAmount, lineTaxableAmount, lineTotalAmount, lineSubtotal, lineDiscountAmount, discount;
      const price = parseFloat(item.unit_selling_price || 0);
      const quantity = item.quantity || 0;
      const taxRate = item.tax || 15;

      if (useNewCalculationMethod) {
        // NEW METHOD: Calculate using the exact formula provided
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_selling_price || 0)).toFixed(2);

        // Calculate unit-level values first
        const unitVAT = +((unitTotalAmount - (unitTotalAmount / (1 + (taxRate / 100)))).toFixed(2));
        const unitTaxable = +((unitTotalAmount / (1 + (taxRate / 100))).toFixed(2));

        // Calculate line-level values by multiplying unit values by quantity
        lineVATAmount = +(unitVAT * quantity).toFixed(2);
        lineTaxableAmount = +(unitTotalAmount * quantity - unitVAT * quantity).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineSubtotal = +(price * quantity).toFixed(2);
        lineDiscountAmount = +((price - unitTaxable) * quantity).toFixed(2);
        discount = price > 0 ? +((1 - (unitTaxable / price)) * 100).toFixed(2) : 0;
      } else {
        // OLD METHOD: Calculate based on taxed_discounted_unit_selling_price as unit price
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_selling_price || 0)).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineTaxableAmount = +((unitTotalAmount / (1 + taxRate / 100)) * quantity).toFixed(2);
        lineVATAmount = +((lineTaxableAmount * (taxRate / 100))).toFixed(2);
        lineSubtotal = +(lineTaxableAmount + lineVATAmount).toFixed(2);
        lineDiscountAmount = +(lineSubtotal - lineTaxableAmount).toFixed(2);
        discount = item.discount || 0;
      }

      insertValues.push(`(
        '${returnItemId}',
        '${item.return_id}',
        (SELECT OrderItemID FROM SalesOrderItems WHERE OrderID = '${item.order_id}' AND ItemNumber = ${item.item_id} LIMIT 1),
        '${item.order_id}',
        '${barcode}',
        ${itemInfo.ItemID ? `'${itemInfo.ItemID}'` : 'NULL'},
        '${(itemInfo.ItemName || `Item ${item.item_id}`).replace(/'/g, "''")}',
        'PCE',
        ${quantity},
        ${price.toFixed(2)},
        ${parseFloat(item.unit_purchase_price || 0).toFixed(2)},
        ${discount},
        ${taxRate},
        ${lineSubtotal}, ${lineDiscountAmount}, ${lineTaxableAmount}, ${lineVATAmount}, ${lineTotalAmount}, ${unitTotalAmount},
        ${item.item_id},
        'S',
        'Return',
        1,
        '${timestamp}',
        '${timestamp}',
        '${item.user || 'admin'}',
        '${item.user || 'admin'}'
      )`);
    }

    // Insert return items
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO SalesOrderReturnItems (
          ReturnItemID, ReturnID, OrderItemID, OrderID,
          Barcode, ItemID, ItemName, UnitID,
          Quantity, Price, UnitCost, Discount,
          TaxRate, LineSubtotal, LineDiscountAmount, LineTaxableAmount,
          LineVATAmount, LineTotalAmount, UnitTotalAmount, ItemNumber,
          TaxID, Reason, Processed, CreatedAt, UpdatedAt,
          CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    totalProcessed += returnItems.length;
    console.log(`Processed ${totalProcessed}/${totalReturnItems} return items (${Math.round(totalProcessed/totalReturnItems*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} sales order return items.`);
  return totalProcessed;
}

// Payments are now handled by the migrate-payments-to-transactions module

/**
 * Main function to copy sales orders, items, returns, and return items
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {boolean} useNewCalculationMethod - Whether to use new unit-based calculation method
 * @param {Function} callback - Callback function to call when done
 */
async function migrateSalesOrders(db, startTime, useNewCalculationMethod, callback) {
  try {
    // Step 1: Clear all related tables
    await clearTables(db);

    // Generate timestamp for all records
    const timestamp = new Date().toISOString();

    console.log('Starting copy-only migration...');

    // Step 2: Create mapping tables
    await createMappingTables(db);

    // Step 3: Copy sales_orders to SalesOrders
    await copySalesOrders(db, timestamp);

    // Step 4: Copy sales_order_change_additions to SalesOrderItems
    await copySalesOrderItems(db, timestamp, useNewCalculationMethod);

    // Step 5: Copy sales_order_changes to SalesOrderReturns
    await copySalesOrderReturns(db, timestamp);

    // Step 6: Copy sales_order_change_returns to SalesOrderReturnItems
    await copySalesOrderReturnItems(db, timestamp, useNewCalculationMethod);

    // Note: Payments are now handled by the migrate-payments-to-transactions module

    // Step 7: Perform calculations
    console.log('Performing calculations...');
    const calcStartTime = performance.now();
    await performCalculations(db, timestamp);
    const calcEndTime = performance.now();
    console.log(`Calculations completed in ${((calcEndTime - calcStartTime) / 1000).toFixed(2)} seconds`);

    // Clean up temporary tables
    await run(db, 'DROP TABLE IF EXISTS order_mapping');
    await run(db, 'DROP TABLE IF EXISTS return_mapping');

    // Log performance metrics
    const endTime = performance.now();
    const seconds = (endTime - startTime) / 1000;
    console.log(`Migration completed in ${seconds.toFixed(2)} seconds`);

    callback(null);
  } catch (err) {
    callback(err);
  }
}

/**
 * Perform calculations on the migrated data
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} batchSize - Number of records to process in each batch
 */
async function performCalculations(db, timestamp, batchSize = 10000) {
  console.log('Creating indexes to optimize calculations...');

  // Add indexes for SalesOrderItems
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_salesorderitems_orderid ON SalesOrderItems(OrderID)');
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_salesorderitems_itemnumber ON SalesOrderItems(ItemNumber)');

  // Add indexes for SalesOrderReturnItems
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_salesorderreturnitems_returnid ON SalesOrderReturnItems(ReturnID)');
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_salesorderreturnitems_orderid ON SalesOrderReturnItems(OrderID)');
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_salesorderreturnitems_orderitemid ON SalesOrderReturnItems(OrderItemID)');

  // Add indexes for SalesOrderReturns
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_salesorderreturns_orderid ON SalesOrderReturns(OrderID)');

  // Add index for Transactions
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_transactions_referenceid ON Transactions(ReferenceID)');

  console.log('Indexes created successfully.');

  console.log('Step 1: Calculating values for SalesOrderReturnItems...');

  // Get total count of return items
  const returnItemsCount = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderReturnItems');
  console.log(`Found ${returnItemsCount.count} return items to calculate.`);

  // Process in batches - use larger batch size for smaller tables
  const returnItemBatchSize = Math.min(batchSize * 2, returnItemsCount.count); // Double batch size for return items
  const returnItemBatches = Math.ceil(returnItemsCount.count / returnItemBatchSize);

  for (let batch = 0; batch < returnItemBatches; batch++) {
    const offset = batch * returnItemBatchSize;
    console.log(`Processing return items batch ${batch + 1}/${returnItemBatches} (offset: ${offset})...`);

    // Calculations are now done during insert, no need for update

    await run(db, `
      UPDATE SalesOrderReturnItems
      SET
        LineDiscountAmount = ROUND(LineSubtotal - LineTaxableAmount, 2)
      WHERE rowid IN (
        SELECT rowid FROM SalesOrderReturnItems
        ORDER BY rowid
        LIMIT ${returnItemBatchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('Step 2: Calculating values for SalesOrderReturns...');

  // Get total count of returns
  const returnsCount = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderReturns');
  console.log(`Found ${returnsCount.count} returns to calculate.`);

  // Process in batches - use larger batch size for smaller tables
  const returnBatchSize = Math.min(batchSize * 3, returnsCount.count); // Triple batch size for returns
  const returnBatches = Math.ceil(returnsCount.count / returnBatchSize);

  for (let batch = 0; batch < returnBatches; batch++) {
    const offset = batch * returnBatchSize;
    console.log(`Processing returns batch ${batch + 1}/${returnBatches} (offset: ${offset})...`);

    await run(db, `
      UPDATE SalesOrderReturns
      SET
        InvoiceSubtotal = (
          SELECT COALESCE(SUM(LineSubtotal), 0)
          FROM SalesOrderReturnItems
          WHERE ReturnID = SalesOrderReturns.ReturnID
        ),
        InvoiceTaxableAmount = (
          SELECT COALESCE(SUM(LineTaxableAmount), 0)
          FROM SalesOrderReturnItems
          WHERE ReturnID = SalesOrderReturns.ReturnID
        ),
        InvoiceVATAmount = (
          SELECT COALESCE(SUM(LineVATAmount), 0)
          FROM SalesOrderReturnItems
          WHERE ReturnID = SalesOrderReturns.ReturnID
        ),
        InvoiceTotalAmount = (
          SELECT COALESCE(SUM(LineTotalAmount), 0)
          FROM SalesOrderReturnItems
          WHERE ReturnID = SalesOrderReturns.ReturnID
        ),
        ReturnItemsCount = (
          SELECT COUNT(*)
          FROM SalesOrderReturnItems
          WHERE ReturnID = SalesOrderReturns.ReturnID
        )
      WHERE rowid IN (
        SELECT rowid FROM SalesOrderReturns
        ORDER BY rowid
        LIMIT ${returnBatchSize} OFFSET ${offset}
      )
    `);

    await run(db, `
      UPDATE SalesOrderReturns
      SET
        InvoiceDiscountAmount = ROUND(InvoiceSubtotal - InvoiceTaxableAmount, 2)
      WHERE rowid IN (
        SELECT rowid FROM SalesOrderReturns
        ORDER BY rowid
        LIMIT ${returnBatchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('Step 3: Calculating values for SalesOrderItems...');

  // Get total count of items
  const itemsCount = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderItems');
  console.log(`Found ${itemsCount.count} items to calculate.`);

  // Process in batches - use smaller batch size for larger tables
  const itemBatchSize = Math.min(batchSize, itemsCount.count);
  const itemBatches = Math.ceil(itemsCount.count / itemBatchSize);

  for (let batch = 0; batch < itemBatches; batch++) {
    const offset = batch * itemBatchSize;
    console.log(`Processing items batch ${batch + 1}/${itemBatches} (offset: ${offset})...`);

    // Calculations are now done during insert, no need for update

    await run(db, `
      UPDATE SalesOrderItems
      SET
        LineDiscountAmount = ROUND(LineSubtotal - LineTaxableAmount, 2)
      WHERE rowid IN (
        SELECT rowid FROM SalesOrderItems
        ORDER BY rowid
        LIMIT ${itemBatchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('Step 4: Calculating values for SalesOrders...');

  // Get total count of orders
  const ordersCount = await get(db, 'SELECT COUNT(*) as count FROM SalesOrders');
  console.log(`Found ${ordersCount.count} orders to calculate.`);

  // Process in batches - use smaller batch size for larger tables
  const orderBatchSize = Math.min(batchSize, ordersCount.count);
  const orderBatches = Math.ceil(ordersCount.count / orderBatchSize);

  for (let batch = 0; batch < orderBatches; batch++) {
    const offset = batch * orderBatchSize;
    console.log(`Processing orders batch ${batch + 1}/${orderBatches} (offset: ${offset})...`);

    await run(db, `
      UPDATE SalesOrders
      SET
        InvoiceSubtotal = (
          SELECT COALESCE(SUM(LineSubtotal), 0)
          FROM SalesOrderItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        InvoiceTaxableAmount = (
          SELECT COALESCE(SUM(LineTaxableAmount), 0)
          FROM SalesOrderItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        InvoiceVATAmount = (
          SELECT COALESCE(SUM(LineVATAmount), 0)
          FROM SalesOrderItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        InvoiceTotalAmount = (
          SELECT COALESCE(SUM(LineTotalAmount), 0)
          FROM SalesOrderItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        TotalReturnAmount = (
          SELECT COALESCE(SUM(InvoiceTotalAmount), 0)
          FROM SalesOrderReturns
          WHERE OrderID = SalesOrders.OrderID
        ),
        SalesItemsCount = (
          SELECT COALESCE(SUM(Quantity), 0)
          FROM SalesOrderItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        ReturnsItemsCount = (
          SELECT COALESCE(SUM(Quantity), 0)
          FROM SalesOrderReturnItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        SalesEntriesCount = (
          SELECT COUNT(DISTINCT OrderItemID)
          FROM SalesOrderItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        ReturnsEntriesCount = (
          SELECT COUNT(DISTINCT ReturnItemID)
          FROM SalesOrderReturnItems
          WHERE OrderID = SalesOrders.OrderID
        ),
        ReturnsCount = (
          SELECT COUNT(*)
          FROM SalesOrderReturns
          WHERE OrderID = SalesOrders.OrderID
        )
      WHERE rowid IN (
        SELECT rowid FROM SalesOrders
        ORDER BY rowid
        LIMIT ${batchSize} OFFSET ${offset}
      )
    `);

    await run(db, `
      UPDATE SalesOrders
      SET
        InvoiceDiscountAmount = ROUND(InvoiceSubtotal - InvoiceTaxableAmount, 2),
        TotalAfterReturns = ROUND(InvoiceTotalAmount - TotalReturnAmount, 2),
        -- PaidAmount and RemainingAmount will be calculated later by update-sales-calculations.js
        -- after all transactions have been created
        PaidAmount = 0,
        RemainingAmount = 0
      WHERE rowid IN (
        SELECT rowid FROM SalesOrders
        ORDER BY rowid
        LIMIT ${batchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('All calculations completed successfully.');
}

module.exports = { migrateSalesOrders };
