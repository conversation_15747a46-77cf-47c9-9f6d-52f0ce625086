/**
 * Inventory Transactions Migration Module
 *
 * This module creates inventory transactions for initial quantities and cost prices.
 * It is designed to be the final step in the migration process.
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create inventory transactions for initial quantities based on difference calculation
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createInitialInventoryTransactions(db, batchSize = 1000) {
  console.log('Creating inventory transactions for initial quantities based on difference calculation...');

  // Get total count of items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM Items');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;
  const initialDate = '2020-07-01 08:27:59'; // Fixed date for all initial transactions

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of items with their current quantities from old db and sum of inventory transactions
    const items = await all(db, `
      SELECT
        i.ItemID,
        i.ItemNumber,
        i.Name,
        old.quantity as current_quantity_old_db,
        old.purchase_price,
        COALESCE(
          (SELECT SUM(QuantityChange)
           FROM InventoryTransactions
           WHERE ItemID = i.ItemID AND ReferenceType != 'OpeningBalance'),
          0
        ) as sum_other_transactions
      FROM Items i
      JOIN old_db.items old ON i.ItemNumber = old.id
      ORDER BY i.ItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement for inventory transactions
    let transactionValues = [];

    for (const item of items) {
      // Calculate the initial quantity needed so that: Initial + Sum of Others = Old DB Quantity
      const currentQuantityOldDb = item.current_quantity_old_db || 0;
      const sumOtherTransactions = item.sum_other_transactions || 0;
      const initialQuantityNeeded = currentQuantityOldDb - sumOtherTransactions;

      // Only create a transaction if there's an initial quantity needed
      if (initialQuantityNeeded !== 0) {
        const transactionId = uuidv4();
        const referenceNumber = `INIT-${item.ItemNumber}`;

        // Get cost price (default to 0 if not available)
        const costPrice = item.purchase_price || 0;

        transactionValues.push(`(
          '${transactionId}',
          '${item.ItemID}',
          'OpeningBalance',
          ${initialQuantityNeeded},
          ${costPrice},
          '${initialDate}',
          'Initial inventory: ${referenceNumber} (Old DB Qty: ${currentQuantityOldDb}, Sum Others: ${sumOtherTransactions}, Initial Needed: ${initialQuantityNeeded})',
          'migration'
        )`);
      }
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);

      console.log(`Inserted ${transactionValues.length} inventory transactions.`);
    } else {
      console.log('No inventory transactions to insert in this batch (all differences were zero).');
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created inventory transactions for ${totalProcessed} items.`);
}

/**
 * Main function to migrate inventory transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateInventoryTransactions(db, startTime, callback) {
  try {
    console.log('Starting inventory transactions migration...');

    // Clear existing initial inventory transactions
    console.log('Clearing existing initial inventory transactions...');
    await run(db, "DELETE FROM InventoryTransactions WHERE ReferenceType IN ('Initial', 'OpeningBalance')");

    // Create inventory transactions for initial quantities and cost prices
    await createInitialInventoryTransactions(db);

    const endTime = performance.now();
    console.log(`Inventory transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating inventory transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateInventoryTransactions };
