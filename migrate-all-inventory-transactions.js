/**
 * All Inventory Transactions Migration Module
 *
 * This module creates inventory transactions for:
 * 1. Purchase orders (increases inventory)
 * 2. Purchase order returns (decreases inventory)
 * 3. Sales orders (decreases inventory)
 * 4. Sales order returns (increases inventory)
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create inventory transactions for purchase orders
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseInventoryTransactions(db, batchSize = 10000) {
  console.log('Creating inventory transactions for purchase orders...');

  // Get total count of purchase order items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderItems');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} purchase order items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order items
    const items = await all(db, `
      SELECT
        i.OrderItemID, i.OrderID, i.ItemID, i.Quantity, i.Price, i.UnitCost,
        p.OrderDate, p.PurchaseOrderNumber, p.InvoiceNumber
      FROM PurchaseOrderItems i
      JOIN PurchaseOrders p ON i.OrderID = p.OrderID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.OrderItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const item of items) {
      // Create a transaction for the purchase (increases inventory)
      const transactionId = uuidv4();

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        'PurchaseCredit',
        ${item.Quantity},
        ${item.Price},
        '${item.OrderDate}',
        'Purchase: ${item.PurchaseOrderNumber} - ${item.InvoiceNumber}',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created inventory transactions for ${totalProcessed} purchase order items.`);
}

/**
 * Create inventory transactions for purchase order returns
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseReturnInventoryTransactions(db, batchSize = 10000) {
  console.log('Creating inventory transactions for purchase order returns...');

  // Get total count of purchase order return items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderReturnItems');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} purchase order return items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order return items
    const items = await all(db, `
      SELECT
        i.ReturnItemID, i.ReturnID, i.ItemID, i.Quantity, i.Price, i.UnitCost,
        r.ReturnDate, r.ReturnNumber, r.FinalInvoiceNumber
      FROM PurchaseOrderReturnItems i
      JOIN PurchaseOrderReturns r ON i.ReturnID = r.ReturnID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.ReturnItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const item of items) {
      // Create a transaction for the purchase return (decreases inventory)
      const transactionId = uuidv4();

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        'ReturnCreditPurchase',
        ${-item.Quantity}, /* Negative quantity for returns */
        ${item.Price},
        '${item.ReturnDate}',
        'Purchase Return: ${item.ReturnNumber} - ${item.FinalInvoiceNumber}',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created inventory transactions for ${totalProcessed} purchase order return items.`);
}

/**
 * Create inventory transactions for sales orders
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesInventoryTransactions(db, batchSize = 10000) {
  console.log('Creating inventory transactions for sales orders...');

  // Get total count of sales order items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderItems');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} sales order items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales order items with customer info
    const items = await all(db, `
      SELECT
        i.OrderItemID, i.OrderID, i.ItemID, i.Quantity, i.Price,
        s.OrderDate, s.SalesOrderNumber, s.InvoiceNumber, s.CustomerID
      FROM SalesOrderItems i
      JOIN SalesOrders s ON i.OrderID = s.OrderID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.OrderItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const item of items) {
      // Create a transaction for the sale (decreases inventory)
      const transactionId = uuidv4();

      // Determine if it's a cash or credit sale
      const referenceType = item.CustomerID === 'C-UNKNOWN' ? 'SaleCash' : 'SaleCredit';
      const saleType = item.CustomerID === 'C-UNKNOWN' ? 'Cash Sale' : 'Credit Sale';

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        '${referenceType}',
        ${-item.Quantity}, /* Negative quantity for sales */
        ${item.Price},
        '${item.OrderDate}',
        '${saleType}: ${item.SalesOrderNumber} - ${item.InvoiceNumber}',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created inventory transactions for ${totalProcessed} sales order items.`);
}

/**
 * Create inventory transactions for sales order returns
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesReturnInventoryTransactions(db, batchSize = 10000) {
  console.log('Creating inventory transactions for sales order returns...');

  // Get total count of sales order return items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderReturnItems');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} sales order return items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales order return items with customer info
    const items = await all(db, `
      SELECT
        i.ReturnItemID, i.ReturnID, i.ItemID, i.Quantity, i.Price,
        r.ReturnDate, r.ReturnNumber, r.FinalInvoiceNumber, s.CustomerID
      FROM SalesOrderReturnItems i
      JOIN SalesOrderReturns r ON i.ReturnID = r.ReturnID
      JOIN SalesOrders s ON r.OrderID = s.OrderID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.ReturnItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const item of items) {
      // Create a transaction for the sales return (increases inventory)
      const transactionId = uuidv4();

      // Determine if it's a return of cash or credit sale
      const referenceType = item.CustomerID === 'C-UNKNOWN' ? 'ReturnCashSale' : 'ReturnCreditSale';
      const returnType = item.CustomerID === 'C-UNKNOWN' ? 'Cash Sale Return' : 'Credit Sale Return';

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        '${referenceType}',
        ${item.Quantity}, /* Positive quantity for returns */
        ${item.Price},
        '${item.ReturnDate}',
        '${returnType}: ${item.ReturnNumber} - ${item.FinalInvoiceNumber}',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created inventory transactions for ${totalProcessed} sales order return items.`);
}

/**
 * Main function to migrate all inventory transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateAllInventoryTransactions(db, startTime, callback) {
  try {
    console.log('Starting all inventory transactions migration...');

    // Clear existing inventory transactions for these types
    console.log('Clearing existing inventory transactions...');
    await run(db, "DELETE FROM InventoryTransactions WHERE ReferenceType IN ('PurchaseOrder', 'PurchaseReturn', 'SalesOrder', 'SalesReturn', 'PurchaseCredit', 'ReturnCreditPurchase', 'SaleCash', 'SaleCredit', 'ReturnCashSale', 'ReturnCreditSale')");

    // Create inventory transactions for purchase orders
    await createPurchaseInventoryTransactions(db);

    // Create inventory transactions for purchase order returns
    await createPurchaseReturnInventoryTransactions(db);

    // Create inventory transactions for sales orders
    await createSalesInventoryTransactions(db);

    // Create inventory transactions for sales order returns
    await createSalesReturnInventoryTransactions(db);

    const endTime = performance.now();
    console.log(`All inventory transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating all inventory transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateAllInventoryTransactions };
