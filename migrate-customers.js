/**
 * Customer migration module
 *
 * This module focuses solely on migrating customers from the old database to the new database.
 * It is called by migrator.js which handles the database connections and transactions.
 *
 * This version uses UUID IDs for customers instead of sequential IDs.
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create a customer mapping table to store old and new IDs
 * @param {object} db - Database connection
 */
async function createCustomerMappingTable(db) {
  console.log('Creating customer mapping table...');

  // Create temporary table for mapping customer IDs
  await run(db, `
    CREATE TEMPORARY TABLE customer_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Create index on temporary table
  await run(db, `CREATE INDEX idx_customer_mapping_new_id ON customer_mapping(new_id)`);

  console.log('Customer mapping table created successfully.');
}

/**
 * Migrate customers from old database to new database
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateCustomers(db, startTime, callback) {
  try {
    console.log('Running customer migration with UUIDs...');

    // Remove existing customers
    console.log('Removing existing customers...');
    await run(db, 'DELETE FROM Customers');
    console.log('Existing customers removed successfully.');

    // Create customer mapping table
    await createCustomerMappingTable(db);

    // Get all customers from old database
    const customers = await all(db, 'SELECT * FROM old_db.customers');
    console.log(`Found ${customers.length} customers to migrate.`);

    // Create a special "Unknown" customer with ID "C-UNKNOWN"
    const unknownCustomerId = 'C-UNKNOWN';
    await run(db, `
      INSERT INTO Customers (
        CustomerID, Name, Phone, Email, Address, VATNumber,
        BuildingNumber, StreetName, District, City, Country,
        PostalCode, AdditionalNumber, OtherID, ContactPerson, Notes,
        CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
      ) VALUES (
        'C-UNKNOWN', 'Unknown Customer', NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, 'SA',
        NULL, NULL, 'UNKNOWN', NULL, 'Default unknown customer',
        '${new Date().toISOString()}', '${new Date().toISOString()}', 'migration', 'migration'
      )
    `);

    console.log('Created unknown customer.');

    // Prepare batch insert for customers
    let customerValues = [];
    let mappingValues = [];

    for (const customer of customers) {
      const customerId = uuidv4();

      // Add to customer values
      customerValues.push(`(
        '${customerId}',
        '${(customer.full_name || '').replace(/'/g, "''")}',
        ${customer.mobile ? `'${customer.mobile}'` : 'NULL'},
        '',
        ${customer.address ? `'${customer.address.replace(/'/g, "''")}'` : 'NULL'},
        ${customer.tax_number ? `'${customer.tax_number}'` : 'NULL'},
        '',
        '',
        '',
        '',
        'SA',
        '',
        NULL,
        '${customer.id}',
        NULL,
        ${customer.net_debt > 0 ? `'Migrated. Debt: ${customer.net_debt}'` : "'Migrated. Debt: 0.0'"},
        '${new Date().toISOString()}',
        '${new Date().toISOString()}',
        'migration',
        'migration'
      )`);

      // Add to mapping values
      mappingValues.push(`(${customer.id}, '${customerId}')`);
    }

    // Insert customers
    console.log('Inserting customers...');
    if (customerValues.length > 0) {
      await run(db, `
        INSERT INTO Customers (
          CustomerID, Name, Phone, Email, Address, VATNumber,
          BuildingNumber, StreetName, District, City, Country,
          PostalCode, AdditionalNumber, OtherID, ContactPerson, Notes,
          CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
        ) VALUES ${customerValues.join(',')}
      `);
    }

    // Insert mappings
    console.log('Inserting customer mappings...');
    if (mappingValues.length > 0) {
      await run(db, `
        INSERT INTO customer_mapping (old_id, new_id)
        VALUES ${mappingValues.join(',')}
      `);
    }

    const endTime = performance.now();
    console.log(`Migration completed in ${((endTime - startTime) / 1000).toFixed(2)}s`);
    console.log(`Migrated ${customers.length} customers`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating customers: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateCustomers };
