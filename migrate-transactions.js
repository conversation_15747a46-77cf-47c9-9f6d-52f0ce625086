/**
 * Transactions migration module
 *
 * This module creates accounts for customers and transactions for sales and returns.
 * It is designed to be part of the migration process, but does not handle payments
 * which are now handled by the migrate-payments-to-transactions module.
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create accounts for customers
 * @param {object} db - Database connection
 */
async function createCustomerAccounts(db) {
  console.log('Creating accounts for customers...');

  // Remove ALL existing customer accounts with ParentID = '1300'
  console.log('Removing existing customer accounts...');
  await run(db, "DELETE FROM Accounts WHERE ParentID = '1300'");
  console.log('Existing customer accounts removed successfully.');

  // Get all customers
  const customers = await all(db, 'SELECT CustomerID, Name FROM Customers');
  console.log(`Found ${customers.length} customers to create accounts for.`);

  // Prepare batch insert for accounts
  let accountValues = [];

  for (const customer of customers) {
    // Add to account values
    accountValues.push(`(
      '${customer.CustomerID}',
      '${(customer.Name || 'Customer Account').replace(/'/g, "''")}',
      '1300',
      'Asset',
      'Debit',
      1,
      'Customer account for ${(customer.Name || 'Customer').replace(/'/g, "''")}'
    )`);
  }

  // Insert accounts
  console.log('Inserting customer accounts...');
  if (accountValues.length > 0) {
    await run(db, `
      INSERT INTO Accounts (
        AccountID, AccountName, ParentID, AccountType, NormalBalance, IsLeaf, Description
      ) VALUES ${accountValues.join(',')}
    `);
  }

  console.log(`Created ${customers.length} customer accounts.`);
}

/**
 * Create transactions for sales
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesTransactions(db, batchSize = 10000) {
  console.log('Creating transactions for sales...');

  // Get total count of sales orders
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrders');
  const totalOrders = countResult.count;
  console.log(`Found ${totalOrders} sales orders to process.`);

  // Process in batches
  const batches = Math.ceil(totalOrders / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} orders each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales orders
    const orders = await all(db, `
      SELECT OrderID, CustomerID, InvoiceTotalAmount, OrderDate, SalesOrderNumber, InvoiceNumber
      FROM SalesOrders
      ORDER BY OrderID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) {
      console.log(`No orders found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${orders.length} orders in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const order of orders) {
      // Create a transaction for the sale
      const transactionId = uuidv4();

      if (order.CustomerID === 'C-UNKNOWN') {
        // For sales with unknown customers (cash sales):
        // Debit: Cash (1100) - cash received
        // Credit: Sales Account (4100) - revenue increases
        transactionValues.push(`(
          '${transactionId}',
          '${order.OrderDate}',
          'Cash Sale: ${order.InvoiceNumber}',
          '1100',
          '4100',
          ${order.InvoiceTotalAmount},
          '${order.OrderID}',
          'SaleCash',
          'migration',
          '${new Date().toISOString()}'
        )`);
      } else {
        // For sales with known customers (credit sales):
        // Debit: Customer Account (Asset increases)
        // Credit: Sales Account (4100) - revenue increases
        transactionValues.push(`(
          '${transactionId}',
          '${order.OrderDate}',
          'Credit Sale: ${order.InvoiceNumber}',
          '${order.CustomerID}',
          '4100',
          ${order.InvoiceTotalAmount},
          '${order.OrderID}',
          'SaleCredit',
          'migration',
          '${new Date().toISOString()}'
        )`);
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} sales orders.`);
}

/**
 * Create transactions for returns
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createReturnTransactions(db, batchSize = 10000) {
  console.log('Creating transactions for returns...');

  // Get total count of sales order returns
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderReturns');
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} sales order returns to process.`);

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} returns each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales order returns with customer ID
    const returns = await all(db, `
      SELECT r.ReturnID, r.OrderID, r.InvoiceTotalAmount as ReturnAmount, r.ReturnDate, s.CustomerID,
             r.ReturnNumber, r.FinalInvoiceNumber, s.InvoiceNumber
      FROM SalesOrderReturns r
      JOIN SalesOrders s ON r.OrderID = s.OrderID
      ORDER BY r.ReturnID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) {
      console.log(`No returns found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${returns.length} returns in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const returnItem of returns) {
      // Create a transaction for the return
      const transactionId = uuidv4();

      if (returnItem.CustomerID === 'C-UNKNOWN') {
        // For returns of cash sales:
        // Debit: Sales Returns (4110) - contra revenue account
        // Credit: Cash (1100) - giving money back
        transactionValues.push(`(
          '${transactionId}',
          '${returnItem.ReturnDate}',
          'Cash Sale Return: ${returnItem.FinalInvoiceNumber} for invoice ${returnItem.InvoiceNumber}',
          '4110',
          '1100',
          ${returnItem.ReturnAmount},
          '${returnItem.OrderID}',
          'ReturnCashSale',
          'migration',
          '${new Date().toISOString()}'
        )`);
      } else {
        // For returns of credit sales:
        // Debit: Sales Returns (4110) - contra revenue account
        // Credit: CustomerID - you owe them money now
        transactionValues.push(`(
          '${transactionId}',
          '${returnItem.ReturnDate}',
          'Credit Sale Return: ${returnItem.FinalInvoiceNumber} for invoice ${returnItem.InvoiceNumber}',
          '4110',
          '${returnItem.CustomerID}',
          ${returnItem.ReturnAmount},
          '${returnItem.OrderID}',
          'ReturnCreditSale',
          'migration',
          '${new Date().toISOString()}'
        )`);
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} sales order returns.`);
}

// Payment transactions are now handled by the migrate-payments-to-transactions module

/**
 * Main function to migrate transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateTransactions(db, startTime, callback) {
  try {
    console.log('Starting transactions migration...');

    // Clear existing transactions for sales and returns only
    // (payments are handled by migrate-payments-to-transactions module)
    console.log('Clearing existing sales and return transactions...');
    await run(db, "DELETE FROM Transactions WHERE ReferenceType IN ('SalesOrder', 'SalesOrderReturn', 'SaleCash', 'SaleCredit', 'ReturnCashSale', 'ReturnCreditSale')");

    // Create accounts for customers
    await createCustomerAccounts(db);

    // Create transactions for sales
    await createSalesTransactions(db);

    // Create transactions for returns
    await createReturnTransactions(db);

    // Note: Payment transactions are now handled by the migrate-payments-to-transactions module

    const endTime = performance.now();
    console.log(`Transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateTransactions };
