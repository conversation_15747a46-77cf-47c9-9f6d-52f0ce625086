/**
 * Fix Units Script - Final Version
 *
 * This script fixes the units issue after the migration is complete.
 * It processes the migrate_units.json file to:
 * 1. For sub_items_with_parent: Remove the item but add record in ItemUnits
 * 2. For sub_items_without_parent: Change the name with the parent_name and add ItemUnit with item props
 * 3. Update references in SalesOrderItems and SalesOrderReturnItems
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { performance } = require('perf_hooks');

// Path to the database
const dbPath = 'D:/old/github/n912/spa/pos3/db/db.sqlite3';

// Path to the JSON file
const jsonPath = path.join(__dirname, 'migrate_units.json');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Process sub_items_with_parent
 * @param {object} db - Database connection
 * @param {array} subItemsWithParent - Array of sub items with parent
 */
async function processSubItemsWithParent(db, subItemsWithParent) {
  console.log(`Processing ${subItemsWithParent.length} sub items with parent...`);

  for (const item of subItemsWithParent) {
    console.log(`Processing item ${item.item_id} (${item.name})...`);

    try {
      // 1. Get the ItemID for the parent item
      const parentItem = await get(db, `SELECT ItemID FROM Items WHERE ItemNumber = ?`, [item.parent_id]);
      if (!parentItem) {
        console.log(`Parent item ${item.parent_id} not found, skipping...`);
        continue;
      }

      // 2. Get the ItemID for the sub item
      const subItem = await get(db, `SELECT ItemID FROM Items WHERE ItemNumber = ?`, [item.item_id]);
      if (!subItem) {
        console.log(`Sub item ${item.item_id} not found, skipping...`);
        continue;
      }

      // 3. Use the unit directly (Units table is read-only)
      const unitID = item.unit;

      // 4. Check if the ItemUnit relationship already exists
      const itemUnitExists = await get(db, `
        SELECT * FROM ItemUnits
        WHERE ItemID = ? AND UnitID = ?
      `, [parentItem.ItemID, unitID]);

      if (!itemUnitExists) {
        // 5. Create the ItemUnit relationship for the parent
        console.log(`Creating ItemUnit relationship for ${item.parent_id} and ${item.unit}...`);
        await run(db, `
          INSERT INTO ItemUnits (
            ItemID, UnitID, ConversionFactor,
            Price, TaxedPrice, CreatedAt, UpdatedAt
          )
          VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `, [
          parentItem.ItemID,
          unitID,
          item.conversion_factor,
          parseFloat(item.taxed_price) / 1.15, // Remove tax to get base price
          item.taxed_price
        ]);
      }

      // 6. Update SalesOrderItems to use the parent item with the new unit
      console.log(`Updating SalesOrderItems for item ${item.item_id}...`);
      await run(db, `
        UPDATE SalesOrderItems
        SET ItemID = ?, UnitID = ?
        WHERE ItemNumber = ?
      `, [parentItem.ItemID, unitID, item.item_id]);

      // 7. Update SalesOrderReturnItems to use the parent item with the new unit
      console.log(`Updating SalesOrderReturnItems for item ${item.item_id}...`);
      await run(db, `
        UPDATE SalesOrderReturnItems
        SET ItemID = ?, UnitID = ?
        WHERE ItemNumber = ?
      `, [parentItem.ItemID, unitID, item.item_id]);

      // 8. Delete the sub item from Items table
      console.log(`Deleting sub item ${item.item_id} from Items table...`);
      await run(db, `DELETE FROM Items WHERE ItemNumber = ?`, [item.item_id]);

      console.log(`Successfully processed item ${item.item_id}`);
    } catch (err) {
      console.error(`Error processing item ${item.item_id}: ${err.message}`);
    }
  }
}

/**
 * Process sub_items_without_parent
 * @param {object} db - Database connection
 * @param {array} subItemsWithoutParent - Array of sub items without parent
 */
async function processSubItemsWithoutParent(db, subItemsWithoutParent) {
  console.log(`Processing ${subItemsWithoutParent.length} sub items without parent...`);

  for (const item of subItemsWithoutParent) {
    console.log(`Processing item ${item.item_id} (${item.name})...`);

    try {
      // 1. Get the item
      const itemRecord = await get(db, `SELECT ItemID FROM Items WHERE ItemNumber = ?`, [item.item_id]);
      if (!itemRecord) {
        console.log(`Item ${item.item_id} not found, skipping...`);
        continue;
      }

      // 2. Use the unit directly (Units table is read-only)
      const unitID = item.unit;

      // 3. Check if the ItemUnit relationship already exists
      const itemUnitExists = await get(db, `
        SELECT * FROM ItemUnits
        WHERE ItemID = ? AND UnitID = ?
      `, [itemRecord.ItemID, unitID]);

      if (!itemUnitExists) {
        // 4. Create the ItemUnit relationship
        console.log(`Creating ItemUnit relationship for ${item.item_id} and ${item.unit}...`);
        await run(db, `
          INSERT INTO ItemUnits (
            ItemID, UnitID, ConversionFactor,
            Price, TaxedPrice, CreatedAt, UpdatedAt
          )
          VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        `, [
          itemRecord.ItemID,
          unitID,
          item.conversion_factor,
          parseFloat(item.taxed_price) / 1.15, // Remove tax to get base price
          item.taxed_price
        ]);
      }

      // 5. Update the item name to match the parent name
      console.log(`Updating item name for ${item.item_id} to ${item.parent_name}...`);
      await run(db, `
        UPDATE Items
        SET Name = ?
        WHERE ItemNumber = ?
      `, [item.parent_name, item.item_id]);

      // 6. Update SalesOrderItems to use the new unit
      console.log(`Updating SalesOrderItems for item ${item.item_id}...`);
      await run(db, `
        UPDATE SalesOrderItems
        SET UnitID = ?
        WHERE ItemNumber = ?
      `, [unitID, item.item_id]);

      // 7. Update SalesOrderReturnItems to use the new unit
      console.log(`Updating SalesOrderReturnItems for item ${item.item_id}...`);
      await run(db, `
        UPDATE SalesOrderReturnItems
        SET UnitID = ?
        WHERE ItemNumber = ?
      `, [unitID, item.item_id]);

      console.log(`Successfully processed item ${item.item_id}`);
    } catch (err) {
      console.error(`Error processing item ${item.item_id}: ${err.message}`);
    }
  }
}

/**
 * Main function to fix units
 */
async function fixUnits() {
  const startTime = performance.now();
  console.log('Starting units fix...');

  // Read the JSON file
  const jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

  // Open the database
  const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE, async (err) => {
    if (err) {
      console.error(`Error opening database: ${err.message}`);
      return;
    }

    console.log('Connected to database.');

    try {
      // Begin transaction
      await run(db, 'BEGIN TRANSACTION');

      // Process sub_items_with_parent
      await processSubItemsWithParent(db, jsonData.sub_items_with_parent);

      // Process sub_items_without_parent
      await processSubItemsWithoutParent(db, jsonData.sub_items_without_parent);

      // Commit transaction
      await run(db, 'COMMIT');

      console.log('Units fix completed successfully.');
    } catch (err) {
      // Rollback transaction on error
      await run(db, 'ROLLBACK');
      console.error(`Error fixing units: ${err.message}`);
    } finally {
      // Close the database
      db.close((err) => {
        if (err) {
          console.error(`Error closing database: ${err.message}`);
        } else {
          console.log('Database connection closed.');
        }

        const endTime = performance.now();
        console.log(`Units fix completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);
      });
    }
  });
}

// Run the fix
fixUnits();
