/**
 * Purchase Orders migration module
 *
 * This module copies data from old tables to new tables:
 * - purchase_orders → PurchaseOrders
 * - purchase_order_items → PurchaseOrderItems
 * - purchase_order_returns → PurchaseOrderReturns (if any)
 * - purchase_order_return_items → PurchaseOrderReturnItems (if any)
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Clear all related tables
 * @param {object} db - Database connection
 */
async function clearTables(db) {
  console.log('Clearing purchase order related tables...');

  // Delete from PurchaseOrderReturnItems
  await run(db, 'DELETE FROM PurchaseOrderReturnItems');
  console.log('Cleared PurchaseOrderReturnItems table.');

  // Delete from PurchaseOrderReturns
  await run(db, 'DELETE FROM PurchaseOrderReturns');
  console.log('Cleared PurchaseOrderReturns table.');

  // Delete from PurchaseOrderItems
  await run(db, 'DELETE FROM PurchaseOrderItems');
  console.log('Cleared PurchaseOrderItems table.');

  // Delete from PurchaseOrders
  await run(db, 'DELETE FROM PurchaseOrders');
  console.log('Cleared PurchaseOrders table.');
}

/**
 * Create mapping tables
 * @param {object} db - Database connection
 */
async function createMappingTables(db) {
  console.log('Creating purchase order mapping tables...');

  // Create temporary mapping table for purchase orders
  await run(db, `
    CREATE TEMPORARY TABLE IF NOT EXISTS purchase_order_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Create index on purchase_order_mapping
  await run(db, 'CREATE INDEX IF NOT EXISTS idx_purchase_order_mapping_new_id ON purchase_order_mapping(new_id)');

  // Clear purchase_order_mapping
  await run(db, 'DELETE FROM purchase_order_mapping');
  console.log('Created and cleared purchase order mapping tables.');
}

/**
 * Copy purchase_orders to PurchaseOrders
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copyPurchaseOrders(db, timestamp, batchSize = 10000) {
  console.log('Copying purchase_orders to PurchaseOrders...');

  // Get total count of purchase orders to migrate
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM old_db.purchase_orders WHERE confirmed = 1');
  const totalOrders = countResult.count;
  console.log(`Found ${totalOrders} confirmed purchase orders to copy.`);

  // Process in batches
  const batches = Math.ceil(totalOrders / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} orders each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase orders
    const orders = await all(db, `
      SELECT * FROM old_db.purchase_orders
      WHERE confirmed = 1
      ORDER BY id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) {
      console.log(`No orders found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${orders.length} orders in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];
    let orderMappingValues = [];

    // Get supplier IDs
    const supplierIds = orders.map(order => order.supplier_id).filter(id => id);
    let supplierMap = {};

    if (supplierIds.length > 0) {
      const suppliers = await all(db, `
        SELECT SupplierID, OtherID
        FROM Suppliers
        WHERE OtherID IN (${supplierIds.join(',')})
      `);

      for (const supplier of suppliers) {
        supplierMap[supplier.OtherID] = supplier.SupplierID;
      }
    }

    for (const order of orders) {
      const orderId = uuidv4();
      const purchaseOrderNumber = `PO-${order.id.toString().padStart(6, '0')}`;
      const invoiceNumber = order.invoice_number ? `PO-${order.id}-${order.invoice_number}` : `PO-${order.id}-${Date.now()}`;
      const supplierId = order.supplier_id ? supplierMap[order.supplier_id] || 'S-UNKNOWN' : 'S-UNKNOWN';

      insertValues.push(`(
        '${orderId}',
        '${purchaseOrderNumber}',
        '${invoiceNumber}',
        '${supplierId}',
        '${order.transaction_date || timestamp}',
        1,
        ${order.note ? `'${order.note.replace(/'/g, "''")}'` : 'NULL'},
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        '${invoiceNumber}',
        '${timestamp}',
        '${timestamp}',
        '${order.user || 'admin'}',
        '${order.user || 'admin'}'
      )`);

      orderMappingValues.push(`(${order.id}, '${orderId}')`);
    }

    // Insert purchase orders
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO PurchaseOrders (
          OrderID, PurchaseOrderNumber, InvoiceNumber, SupplierID,
          OrderDate, Processed, Notes, InvoiceSubtotal,
          InvoiceDiscountAmount, InvoiceTaxableAmount, InvoiceVATAmount,
          InvoiceTotalAmount, TotalReturnAmount, TotalAfterReturns,
          PaidAmount, RemainingAmount, PurchaseItemsCount, ReturnsItemsCount,
          PurchaseEntriesCount, ReturnsEntriesCount, ReturnsCount,
          FinalInvoiceNumber, CreatedAt, UpdatedAt,
          CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    // Insert order mappings
    if (orderMappingValues.length > 0) {
      await run(db, `
        INSERT INTO purchase_order_mapping (old_id, new_id)
        VALUES ${orderMappingValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} purchase orders.`);
  return totalProcessed;
}

/**
 * Copy purchase_order_details to PurchaseOrderItems
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {boolean} useNewCalculationMethod - Whether to use new unit-based calculation method
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copyPurchaseOrderItems(db, timestamp, useNewCalculationMethod, batchSize = 10000) {
  console.log('Copying purchase_order_details to PurchaseOrderItems...');

  // Get total count of purchase order items to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.purchase_order_details i
    JOIN old_db.purchase_orders o ON i.purchase_order_id = o.id
    WHERE o.confirmed = 1
  `);
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} purchase order items to copy.`);

  // Pre-fetch all item data to avoid individual lookups
  console.log('Pre-fetching item data...');
  const itemsData = await all(db, `
    SELECT i.ItemID, i.ItemNumber,
           COALESCE((SELECT name FROM old_db.items WHERE id = i.ItemNumber), 'Item ' || i.ItemNumber) as ItemName
    FROM Items i
  `);

  // Create a lookup map for quick access
  const itemsMap = {};
  for (const item of itemsData) {
    itemsMap[item.ItemNumber] = {
      ItemID: item.ItemID,
      ItemName: item.ItemName
    };
  }
  console.log(`Pre-fetched data for ${itemsData.length} items.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of items
    const items = await all(db, `
      SELECT
        i.*,
        o.user,
        om.new_id as order_id
      FROM old_db.purchase_order_details i
      JOIN old_db.purchase_orders o ON i.purchase_order_id = o.id
      JOIN purchase_order_mapping om ON i.purchase_order_id = om.old_id
      WHERE o.confirmed = 1
      ORDER BY i.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];

    for (const item of items) {
      const itemId = uuidv4();
      const barcode = `ABC${item.item_id.toString().padStart(7, '0')}`;
      const itemInfo = itemsMap[item.item_id] || { ItemID: null, ItemName: `Item ${item.item_id}` };

      let unitTotalAmount, lineVATAmount, lineTaxableAmount, lineTotalAmount, lineSubtotal, lineDiscountAmount, discount;
      const price = parseFloat(item.unit_purchase_price || 0);
      const quantity = item.quantity || 0;
      const taxRate = item.tax || 15;

      if (useNewCalculationMethod) {
        // NEW METHOD: Calculate using the exact formula provided
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_purchase_price || 0)).toFixed(2);

        // Calculate unit-level values first
        const unitVAT = +((unitTotalAmount - (unitTotalAmount / (1 + (taxRate / 100)))).toFixed(2));
        const unitTaxable = +((unitTotalAmount / (1 + (taxRate / 100))).toFixed(2));

        // Calculate line-level values by multiplying unit values by quantity
        lineVATAmount = +(unitVAT * quantity).toFixed(2);
        lineTaxableAmount = +(unitTotalAmount * quantity - unitVAT * quantity).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineSubtotal = +(price * quantity).toFixed(2);
        lineDiscountAmount = +((price - unitTaxable) * quantity).toFixed(2);
        discount = price > 0 ? +((1 - (unitTaxable / price)) * 100).toFixed(2) : 0;
      } else {
        // OLD METHOD: Calculate based on taxed_discounted_unit_purchase_price as unit price
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_purchase_price || 0)).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineTaxableAmount = +((unitTotalAmount / (1 + taxRate / 100)) * quantity).toFixed(2);
        lineVATAmount = +((lineTaxableAmount * (taxRate / 100))).toFixed(2);
        lineSubtotal = +(lineTaxableAmount + lineVATAmount).toFixed(2);
        lineDiscountAmount = +(lineSubtotal - lineTaxableAmount).toFixed(2);
        discount = item.discount || 0;
      }

      insertValues.push(`(
        '${itemId}',
        '${item.order_id}',
        '${barcode}',
        ${itemInfo.ItemID ? `'${itemInfo.ItemID}'` : 'NULL'},
        '${(itemInfo.ItemName || `Item ${item.item_id}`).replace(/'/g, "''")}',
        'PCE',
        ${quantity},
        ${price.toFixed(2)},
        0, /* Ignoring cost as requested */
        ${discount},
        ${taxRate},
        ${lineSubtotal}, ${lineDiscountAmount}, ${lineTaxableAmount}, ${lineVATAmount}, ${lineTotalAmount}, ${unitTotalAmount},
        ${item.item_id},
        'S',
        '${timestamp}',
        '${timestamp}',
        '${item.user || 'admin'}',
        '${item.user || 'admin'}'
      )`);
    }

    // Insert items
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO PurchaseOrderItems (
          OrderItemID, OrderID, Barcode, ItemID,
          ItemName, UnitID, Quantity, Price,
          UnitCost, Discount, TaxRate, LineSubtotal,
          LineDiscountAmount, LineTaxableAmount, LineVATAmount,
          LineTotalAmount, UnitTotalAmount, ItemNumber,
          TaxID, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} purchase order items.`);
  return totalProcessed;
}

/**
 * Perform calculations on purchase orders and items
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} batchSize - Number of records to process in each batch
 */
async function performCalculations(db, timestamp, batchSize = 10000) {
  console.log('Performing calculations on purchase orders and items...');

  // Get counts
  const itemsCount = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderItems');
  const ordersCount = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrders');

  console.log(`Found ${itemsCount.count} purchase order items and ${ordersCount.count} purchase orders to update.`);

  // Calculate item values
  const itemBatchSize = Math.min(batchSize, itemsCount.count);
  const itemBatches = Math.ceil(itemsCount.count / itemBatchSize);

  for (let batch = 0; batch < itemBatches; batch++) {
    const offset = batch * itemBatchSize;
    console.log(`Processing items batch ${batch + 1}/${itemBatches} (offset: ${offset})...`);

    // Calculations are now done during insert, no need for update
  }

  // Calculate order values
  const orderBatchSize = Math.min(batchSize, ordersCount.count);
  const orderBatches = Math.ceil(ordersCount.count / orderBatchSize);

  for (let batch = 0; batch < orderBatches; batch++) {
    const offset = batch * orderBatchSize;
    console.log(`Processing orders batch ${batch + 1}/${orderBatches} (offset: ${offset})...`);

    await run(db, `
      UPDATE PurchaseOrders
      SET
        InvoiceSubtotal = (
          SELECT COALESCE(SUM(LineSubtotal), 0)
          FROM PurchaseOrderItems
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        InvoiceTaxableAmount = (
          SELECT COALESCE(SUM(LineTaxableAmount), 0)
          FROM PurchaseOrderItems
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        InvoiceVATAmount = (
          SELECT COALESCE(SUM(LineVATAmount), 0)
          FROM PurchaseOrderItems
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        InvoiceTotalAmount = (
          SELECT COALESCE(SUM(LineTotalAmount), 0)
          FROM PurchaseOrderItems
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        TotalReturnAmount = (
          SELECT COALESCE(SUM(InvoiceTotalAmount), 0)
          FROM PurchaseOrderReturns
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        PurchaseItemsCount = (
          SELECT COALESCE(SUM(Quantity), 0)
          FROM PurchaseOrderItems
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        PurchaseEntriesCount = (
          SELECT COUNT(*)
          FROM PurchaseOrderItems
          WHERE OrderID = PurchaseOrders.OrderID
        ),
        ReturnsItemsCount = (
          SELECT COALESCE(SUM(ri.Quantity), 0)
          FROM PurchaseOrderReturnItems ri
          JOIN PurchaseOrderReturns r ON ri.ReturnID = r.ReturnID
          WHERE r.OrderID = PurchaseOrders.OrderID
        ),
        ReturnsEntriesCount = (
          SELECT COUNT(*)
          FROM PurchaseOrderReturnItems ri
          JOIN PurchaseOrderReturns r ON ri.ReturnID = r.ReturnID
          WHERE r.OrderID = PurchaseOrders.OrderID
        ),
        ReturnsCount = (
          SELECT COUNT(*)
          FROM PurchaseOrderReturns
          WHERE OrderID = PurchaseOrders.OrderID
        )
      WHERE rowid IN (
        SELECT rowid FROM PurchaseOrders
        ORDER BY rowid
        LIMIT ${orderBatchSize} OFFSET ${offset}
      )
    `);

    await run(db, `
      UPDATE PurchaseOrders
      SET
        InvoiceDiscountAmount = ROUND(InvoiceSubtotal - InvoiceTaxableAmount, 2),
        TotalAfterReturns = ROUND(InvoiceTotalAmount - TotalReturnAmount, 2),
        -- PaidAmount and RemainingAmount will be calculated later by update-purchase-calculations.js
        -- after all transactions have been created
        PaidAmount = 0,
        RemainingAmount = 0
      WHERE rowid IN (
        SELECT rowid FROM PurchaseOrders
        ORDER BY rowid
        LIMIT ${orderBatchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('All calculations completed successfully.');
}

/**
 * Copy purchase_order_changes to PurchaseOrderReturns
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copyPurchaseOrderReturns(db, timestamp, batchSize = 10000) {
  console.log('Copying purchase_order_changes to PurchaseOrderReturns...');

  // Get total count of purchase order returns to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.purchase_order_changes
    WHERE confirmed = 1
  `);
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} purchase order returns to copy.`);

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} returns each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order returns
    const returns = await all(db, `
      SELECT
        c.*,
        om.new_id as order_id,
        p.invoice_number as original_invoice_number
      FROM old_db.purchase_order_changes c
      JOIN old_db.purchase_orders p ON c.purchase_order_id = p.id
      JOIN purchase_order_mapping om ON c.purchase_order_id = om.old_id
      WHERE c.confirmed = 1
      ORDER BY c.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) {
      console.log(`No returns found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${returns.length} returns in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];
    let returnMappingValues = [];

    for (const ret of returns) {
      const returnId = uuidv4();
      const returnNumber = `PR-${ret.id.toString().padStart(6, '0')}`;
      const finalInvoiceNumber = `PR-${ret.id}-${Date.now()}`;

      insertValues.push(`(
        '${returnId}',
        '${ret.order_id}',
        '${returnNumber}',
        '${ret.transaction_date || timestamp}',
        1, /* Processed */
        '${ret.original_invoice_number}',
        0, 0, 0, 0, 0, 0, 0,
        NULL, /* ZatcaInvoiceNumber */
        '${finalInvoiceNumber}',
        NULL, /* ZatcaStatus */
        '${timestamp}',
        '${timestamp}',
        '${ret.user || 'admin'}',
        '${ret.user || 'admin'}'
      )`);

      returnMappingValues.push(`(${ret.id}, '${returnId}')`);
    }

    // Insert purchase order returns
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO PurchaseOrderReturns (
          ReturnID, OrderID, ReturnNumber, ReturnDate,
          Processed, InvoiceNumber, InvoiceSubtotal, InvoiceDiscountAmount,
          InvoiceTaxableAmount, InvoiceVATAmount, InvoiceTotalAmount,
          ReturnItemsCount, ReturnEntriesCount, ZatcaInvoiceNumber,
          FinalInvoiceNumber, ZatcaStatus, CreatedAt, UpdatedAt,
          CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    // Insert return mappings
    if (returnMappingValues.length > 0) {
      await run(db, `
        INSERT INTO purchase_return_mapping (old_id, new_id)
        VALUES ${returnMappingValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} purchase order returns.`);
  return totalProcessed;
}

/**
 * Copy purchase_order_change_returns to PurchaseOrderReturnItems
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {boolean} useNewCalculationMethod - Whether to use new unit-based calculation method
 * @param {number} batchSize - Number of records to process in each batch
 */
async function copyPurchaseOrderReturnItems(db, timestamp, useNewCalculationMethod, batchSize = 10000) {
  console.log('Copying purchase_order_change_returns to PurchaseOrderReturnItems...');

  // Get total count of purchase order return items to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.purchase_order_change_returns r
    JOIN old_db.purchase_order_changes c ON r.purchase_order_change_id = c.id
    WHERE c.confirmed = 1
  `);
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} purchase order return items to copy.`);

  // Pre-fetch all item data to avoid individual lookups
  console.log('Pre-fetching item data...');
  const itemsData = await all(db, `
    SELECT i.ItemID, i.ItemNumber,
           COALESCE((SELECT name FROM old_db.items WHERE id = i.ItemNumber), 'Item ' || i.ItemNumber) as ItemName
    FROM Items i
  `);

  // Create a lookup map for quick access
  const itemsMap = {};
  for (const item of itemsData) {
    itemsMap[item.ItemNumber] = {
      ItemID: item.ItemID,
      ItemName: item.ItemName
    };
  }
  console.log(`Pre-fetched data for ${itemsData.length} items.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} items each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of return items
    const items = await all(db, `
      SELECT
        r.*,
        c.user,
        rm.new_id as return_id
      FROM old_db.purchase_order_change_returns r
      JOIN old_db.purchase_order_changes c ON r.purchase_order_change_id = c.id
      JOIN purchase_return_mapping rm ON r.purchase_order_change_id = rm.old_id
      WHERE c.confirmed = 1
      ORDER BY r.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) {
      console.log(`No return items found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${items.length} return items in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let insertValues = [];

    for (const item of items) {
      const itemId = uuidv4();
      const barcode = `ABC${item.item_id.toString().padStart(7, '0')}`;
      const itemInfo = itemsMap[item.item_id] || { ItemID: null, ItemName: `Item ${item.item_id}` };

      let unitTotalAmount, lineVATAmount, lineTaxableAmount, lineTotalAmount, lineSubtotal, lineDiscountAmount, discount;
      const price = parseFloat(item.unit_price || 0);
      const quantity = item.quantity || 0;
      const taxRate = item.tax || 15;

      if (useNewCalculationMethod) {
        // NEW METHOD: Calculate using the exact formula provided
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_price || 0)).toFixed(2);

        // Calculate unit-level values first
        const unitVAT = +((unitTotalAmount - (unitTotalAmount / (1 + (taxRate / 100)))).toFixed(2));
        const unitTaxable = +((unitTotalAmount / (1 + (taxRate / 100))).toFixed(2));

        // Calculate line-level values by multiplying unit values by quantity
        lineVATAmount = +(unitVAT * quantity).toFixed(2);
        lineTaxableAmount = +(unitTotalAmount * quantity - unitVAT * quantity).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineSubtotal = +(price * quantity).toFixed(2);
        lineDiscountAmount = +((price - unitTaxable) * quantity).toFixed(2);
        discount = price > 0 ? +((1 - (unitTaxable / price)) * 100).toFixed(2) : 0;
      } else {
        // OLD METHOD: Calculate based on taxed_discounted_unit_price as unit price
        unitTotalAmount = +(parseFloat(item.taxed_discounted_unit_price || 0)).toFixed(2);
        lineTotalAmount = +(unitTotalAmount * quantity).toFixed(2);
        lineTaxableAmount = +((unitTotalAmount / (1 + taxRate / 100)) * quantity).toFixed(2);
        lineVATAmount = +((lineTaxableAmount * (taxRate / 100))).toFixed(2);
        lineSubtotal = +(lineTaxableAmount + lineVATAmount).toFixed(2);
        lineDiscountAmount = +(lineSubtotal - lineTaxableAmount).toFixed(2);
        discount = item.discount || 0;
      }

      insertValues.push(`(
        '${itemId}',
        '${item.return_id}',
        NULL, /* OrderItemID */
        NULL, /* OrderID */
        '${barcode}',
        ${itemInfo.ItemID ? `'${itemInfo.ItemID}'` : 'NULL'},
        '${(itemInfo.ItemName || `Item ${item.item_id}`).replace(/'/g, "''")}',
        'PCE',
        ${quantity},
        ${price.toFixed(2)},
        0, /* Ignoring cost as requested */
        ${discount},
        ${taxRate},
        NULL, /* ExemptionReasonText */
        NULL, /* ExemptionReasonCodeID */
        ${lineSubtotal}, ${lineDiscountAmount}, ${lineTaxableAmount}, ${lineVATAmount}, ${lineTotalAmount}, ${unitTotalAmount},
        NULL, /* ItemIdentifier */
        'S',
        ${item.item_id},
        NULL, /* Reason */
        1, /* Processed */
        '${timestamp}',
        '${timestamp}',
        '${item.user || 'admin'}',
        '${item.user || 'admin'}'
      )`);
    }

    // Insert return items
    if (insertValues.length > 0) {
      await run(db, `
        INSERT INTO PurchaseOrderReturnItems (
          ReturnItemID, ReturnID, OrderItemID, OrderID,
          Barcode, ItemID, ItemName, UnitID,
          Quantity, Price, UnitCost, Discount,
          TaxRate, ExemptionReasonText, ExemptionReasonCodeID, LineSubtotal,
          LineDiscountAmount, LineTaxableAmount, LineVATAmount, LineTotalAmount,
          UnitTotalAmount, ItemIdentifier, TaxID, ItemNumber,
          Reason, Processed, CreatedAt, UpdatedAt,
          CreatedBy, UpdatedBy
        ) VALUES ${insertValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} return items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Copied ${totalProcessed} purchase order return items.`);
  return totalProcessed;
}

/**
 * Main function to migrate purchase orders
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {boolean} useNewCalculationMethod - Whether to use new unit-based calculation method
 * @param {Function} callback - Callback function to call when done
 */
async function migratePurchaseOrders(db, startTime, useNewCalculationMethod, callback) {
  try {
    console.log('Starting purchase orders migration...');

    // Step 1: Clear all related tables
    await clearTables(db);

    // Generate timestamp for all records
    const timestamp = new Date().toISOString();

    // Step 2: Create mapping tables
    await createMappingTables(db);

    // Create return mapping table
    await run(db, `
      CREATE TEMPORARY TABLE IF NOT EXISTS purchase_return_mapping (
        old_id INTEGER PRIMARY KEY,
        new_id TEXT NOT NULL
      )
    `);

    // Create index on purchase_return_mapping
    await run(db, 'CREATE INDEX IF NOT EXISTS idx_purchase_return_mapping_new_id ON purchase_return_mapping(new_id)');

    // Clear purchase_return_mapping
    await run(db, 'DELETE FROM purchase_return_mapping');
    console.log('Created and cleared purchase return mapping table.');

    // Step 3: Copy purchase_orders to PurchaseOrders
    await copyPurchaseOrders(db, timestamp);

    // Step 4: Copy purchase_order_items to PurchaseOrderItems
    await copyPurchaseOrderItems(db, timestamp, useNewCalculationMethod);

    // Step 5: Copy purchase_order_changes to PurchaseOrderReturns
    await copyPurchaseOrderReturns(db, timestamp);

    // Step 6: Copy purchase_order_change_returns to PurchaseOrderReturnItems
    await copyPurchaseOrderReturnItems(db, timestamp, useNewCalculationMethod);

    // Step 7: Perform calculations
    console.log('Performing calculations...');
    const calcStartTime = performance.now();
    await performCalculations(db, timestamp);
    const calcEndTime = performance.now();
    console.log(`Calculations completed in ${((calcEndTime - calcStartTime) / 1000).toFixed(2)} seconds`);

    const endTime = performance.now();
    console.log(`Purchase orders migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating purchase orders: ${err.message}`);
    callback(err);
  }
}

module.exports = { migratePurchaseOrders };
