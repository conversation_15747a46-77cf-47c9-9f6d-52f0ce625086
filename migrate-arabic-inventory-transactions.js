/**
 * Arabic Inventory Transactions Migration Module
 *
 * This module creates inventory transactions for all operations:
 * - Purchase orders (increases inventory)
 * - Purchase returns (decreases inventory)
 * - Sales orders (decreases inventory)
 * - Sales returns (increases inventory)
 * - Opening balances (initial inventory)
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Clear all existing inventory transactions
 * @param {object} db - Database connection
 */
async function clearInventoryTransactions(db) {
  console.log('Clearing all existing inventory transactions...');
  await run(db, 'DELETE FROM InventoryTransactions');
  console.log('All inventory transactions cleared.');
}

/**
 * Create purchase inventory transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseInventoryTransactions(db, batchSize = 1000) {
  console.log('Creating purchase inventory transactions...');

  // Get total count of purchase order items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderItems WHERE ItemID IS NOT NULL');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} purchase order items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order items with conversion factors
    const items = await all(db, `
      SELECT
        i.OrderItemID, i.OrderID, i.ItemID, i.Quantity, i.Price,
        p.OrderDate, p.PurchaseOrderNumber, p.InvoiceNumber,
        COALESCE(iu.ConversionFactor, 1) as conversion_factor
      FROM PurchaseOrderItems i
      JOIN PurchaseOrders p ON i.OrderID = p.OrderID
      LEFT JOIN ItemUnits iu ON i.ItemID = iu.ItemID AND iu.UnitID = i.UnitID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.OrderItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let transactionValues = [];

    for (const item of items) {
      const transactionId = uuidv4();
      // Calculate actual quantity change using conversion factor
      const actualQuantityChange = item.Quantity * item.conversion_factor;

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        'PURCHASE',
        ${actualQuantityChange},
        ${item.Price},
        '${item.OrderDate}',
        'شراء: ${item.PurchaseOrderNumber} - ${item.InvoiceNumber} (كمية: ${item.Quantity} × معامل: ${item.conversion_factor})',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created ${totalProcessed} purchase inventory transactions.`);
}

/**
 * Create purchase return inventory transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseReturnInventoryTransactions(db, batchSize = 1000) {
  console.log('Creating purchase return inventory transactions...');

  // Get total count of purchase order return items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderReturnItems WHERE ItemID IS NOT NULL');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} purchase order return items to process.`);

  if (totalItems === 0) {
    console.log('No purchase return items to process.');
    return;
  }

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order return items with conversion factors
    const items = await all(db, `
      SELECT
        i.ReturnItemID, i.ReturnID, i.ItemID, i.Quantity, i.Price,
        r.ReturnDate, r.ReturnNumber, r.FinalInvoiceNumber,
        COALESCE(iu.ConversionFactor, 1) as conversion_factor
      FROM PurchaseOrderReturnItems i
      JOIN PurchaseOrderReturns r ON i.ReturnID = r.ReturnID
      LEFT JOIN ItemUnits iu ON i.ItemID = iu.ItemID AND iu.UnitID = i.UnitID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.ReturnItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let transactionValues = [];

    for (const item of items) {
      const transactionId = uuidv4();
      // Calculate actual quantity change using conversion factor (negative for returns)
      const actualQuantityChange = -(item.Quantity * item.conversion_factor);

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        'PURCHASE_RETURN',
        ${actualQuantityChange},
        ${item.Price},
        '${item.ReturnDate}',
        'مرتجع شراء: ${item.ReturnNumber} - ${item.FinalInvoiceNumber} (كمية: ${item.Quantity} × معامل: ${item.conversion_factor})',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created ${totalProcessed} purchase return inventory transactions.`);
}

/**
 * Create sales inventory transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesInventoryTransactions(db, batchSize = 1000) {
  console.log('Creating sales inventory transactions...');

  // Get total count of sales order items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderItems WHERE ItemID IS NOT NULL');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} sales order items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales order items with customer info and conversion factors
    const items = await all(db, `
      SELECT
        i.OrderItemID, i.OrderID, i.ItemID, i.Quantity, i.Price,
        s.OrderDate, s.SalesOrderNumber, s.InvoiceNumber, s.CustomerID,
        COALESCE(iu.ConversionFactor, 1) as conversion_factor
      FROM SalesOrderItems i
      JOIN SalesOrders s ON i.OrderID = s.OrderID
      LEFT JOIN ItemUnits iu ON i.ItemID = iu.ItemID AND iu.UnitID = i.UnitID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.OrderItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let transactionValues = [];

    for (const item of items) {
      const transactionId = uuidv4();
      const saleType = item.CustomerID === 'C-UNKNOWN' ? 'بيع نقدي' : 'بيع بالآجل';
      // Calculate actual quantity change using conversion factor (negative for sales)
      const actualQuantityChange = -(item.Quantity * item.conversion_factor);

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        'SALE',
        ${actualQuantityChange},
        ${item.Price},
        '${item.OrderDate}',
        '${saleType}: ${item.SalesOrderNumber} - ${item.InvoiceNumber} (كمية: ${item.Quantity} × معامل: ${item.conversion_factor})',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created ${totalProcessed} sales inventory transactions.`);
}

/**
 * Create sales return inventory transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesReturnInventoryTransactions(db, batchSize = 1000) {
  console.log('Creating sales return inventory transactions...');

  // Get total count of sales order return items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderReturnItems WHERE ItemID IS NOT NULL');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} sales order return items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales order return items with customer info and conversion factors
    const items = await all(db, `
      SELECT
        i.ReturnItemID, i.ReturnID, i.ItemID, i.Quantity, i.Price,
        r.ReturnDate, r.ReturnNumber, r.FinalInvoiceNumber, s.CustomerID,
        COALESCE(iu.ConversionFactor, 1) as conversion_factor
      FROM SalesOrderReturnItems i
      JOIN SalesOrderReturns r ON i.ReturnID = r.ReturnID
      JOIN SalesOrders s ON r.OrderID = s.OrderID
      LEFT JOIN ItemUnits iu ON i.ItemID = iu.ItemID AND iu.UnitID = i.UnitID
      WHERE i.ItemID IS NOT NULL
      ORDER BY i.ReturnItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let transactionValues = [];

    for (const item of items) {
      const transactionId = uuidv4();
      const returnType = item.CustomerID === 'C-UNKNOWN' ? 'مرتجع بيع نقدي' : 'مرتجع بيع بالآجل';
      // Calculate actual quantity change using conversion factor (positive for returns)
      const actualQuantityChange = item.Quantity * item.conversion_factor;

      transactionValues.push(`(
        '${transactionId}',
        '${item.ItemID}',
        'SALE_RETURN',
        ${actualQuantityChange},
        ${item.Price},
        '${item.ReturnDate}',
        '${returnType}: ${item.ReturnNumber} - ${item.FinalInvoiceNumber} (كمية: ${item.Quantity} × معامل: ${item.conversion_factor})',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created ${totalProcessed} sales return inventory transactions.`);
}

/**
 * Create opening balance inventory transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createOpeningBalanceInventoryTransactions(db, batchSize = 1000) {
  console.log('Creating opening balance inventory transactions...');

  // Get total count of items
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM Items');
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} items to process.`);

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;
  const openingDate = '2020-07-01 08:27:59'; // Fixed date for all opening balances

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of items with their current quantities from old db and sum of inventory transactions
    const items = await all(db, `
      SELECT
        i.ItemID,
        i.ItemNumber,
        i.Name,
        old.quantity as current_quantity_old_db,
        old.purchase_price,
        COALESCE(
          (SELECT SUM(QuantityChange)
           FROM InventoryTransactions
           WHERE ItemID = i.ItemID AND ReferenceType != 'OPENING_BALANCE'),
          0
        ) as sum_other_transactions
      FROM Items i
      JOIN old_db.items old ON i.ItemNumber = old.id
      ORDER BY i.ItemID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let transactionValues = [];

    for (const item of items) {
      // Calculate the opening balance needed so that: Opening + Sum of Others = Old DB Quantity
      const currentQuantityOldDb = item.current_quantity_old_db || 0;
      const sumOtherTransactions = item.sum_other_transactions || 0;
      const openingBalanceNeeded = currentQuantityOldDb - sumOtherTransactions;

      // Only create a transaction if there's an opening balance needed
      if (openingBalanceNeeded !== 0) {
        const transactionId = uuidv4();
        const referenceNumber = `OPEN-${item.ItemNumber}`;

        // Get cost price (default to 0 if not available)
        const costPrice = item.purchase_price || 0;

        transactionValues.push(`(
          '${transactionId}',
          '${item.ItemID}',
          'OPENING_BALANCE',
          ${openingBalanceNeeded},
          ${costPrice},
          '${openingDate}',
          'رصيد افتتاحي: ${referenceNumber} (كمية قديمة: ${currentQuantityOldDb}, معاملات أخرى: ${sumOtherTransactions}, رصيد مطلوب: ${openingBalanceNeeded})',
          'migration'
        )`);
      }
    }

    // Insert inventory transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created opening balance transactions for ${totalProcessed} items.`);
}

/**
 * Main function to migrate Arabic inventory transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateArabicInventoryTransactions(db, startTime, callback) {
  try {
    console.log('Starting Arabic inventory transactions migration...');

    // Clear all existing inventory transactions
    await clearInventoryTransactions(db);

    // Create purchase inventory transactions
    await createPurchaseInventoryTransactions(db);

    // Create purchase return inventory transactions
    await createPurchaseReturnInventoryTransactions(db);

    // Create sales inventory transactions
    await createSalesInventoryTransactions(db);

    // Create sales return inventory transactions
    await createSalesReturnInventoryTransactions(db);

    // Create opening balance inventory transactions (must be last)
    await createOpeningBalanceInventoryTransactions(db);

    const endTime = performance.now();
    console.log(`Arabic inventory transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating Arabic inventory transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateArabicInventoryTransactions };
