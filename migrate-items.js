/**
 * Item migration module
 *
 * This module migrates categories, items, item units, and barcodes from the old database to the new database.
 * It is called by migrator.js which handles the database connections and transactions.
 */

const { performance } = require('perf_hooks');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

/**
 * Clear all related tables before migration
 * @param {object} db - Database connection
 */
async function clearTables(db) {
  console.log('Clearing related tables...');
  await run(db, "DELETE FROM Barcodes");
  await run(db, "DELETE FROM ItemUnits");
  await run(db, "DELETE FROM Items");
  await run(db, "DELETE FROM Categories");
  console.log('All related tables cleared successfully.');
}

/**
 * Migrate categories from old database to new database
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @returns {Promise<number>} - Number of categories migrated
 */
async function migrateCategories(db, timestamp) {
  console.log('Migrating categories...');

  // Get count of categories to migrate
  const categoryResult = await get(db, 'SELECT COUNT(*) as count FROM old_db.categories');
  const categoryCount = categoryResult.count;
  console.log(`Found ${categoryCount} categories to migrate.`);

  // Migrate all categories at once with a single SQL statement
  const insertCategoriesSql = `
    INSERT INTO Categories (
      CategoryID, Name, Description,
      CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
    )
    SELECT
      lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-4' ||
        substr(hex(randomblob(2)), 2) || '-' ||
        substr('89ab', abs(random()) % 4 + 1, 1) ||
        substr(hex(randomblob(2)), 2) || '-' ||
        hex(randomblob(6))), -- Generate UUID for CategoryID
      name, -- Name
      'Migrated from old category ID: ' || id, -- Description
      '${timestamp}', -- CreatedAt
      '${timestamp}', -- UpdatedAt
      'migration', -- CreatedBy
      'migration' -- UpdatedBy
    FROM old_db.categories
  `;

  await run(db, insertCategoriesSql);
  console.log(`Migrated ${categoryCount} categories.`);

  // Create a mapping table to link old category IDs to new category IDs
  await run(db, `
    CREATE TEMPORARY TABLE category_mapping AS
    SELECT
      oc.id AS old_id,
      nc.CategoryID AS new_id
    FROM old_db.categories oc
    JOIN Categories nc ON oc.name = nc.Name
  `);

  return categoryCount;
}

/**
 * Migrate items from old database to new database
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @returns {Promise<number>} - Number of items migrated
 */
async function migrateItemsTable(db, timestamp) {
  console.log('Migrating items...');

  // Get count of items to migrate
  const itemResult = await get(db, 'SELECT COUNT(*) as count FROM old_db.items');
  const itemCount = itemResult.count;
  console.log(`Found ${itemCount} items to migrate.`);

  // Migrate all items at once with a single SQL statement
  const insertItemsSql = `
    INSERT INTO Items (
      ItemID, ItemNumber, Name, CategoryID, ItemType,
      BaseUnit, TaxID, ExemptionReasonCodeID, IsActive,
      CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
    )
    SELECT
      lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-4' ||
        substr(hex(randomblob(2)), 2) || '-' ||
        substr('89ab', abs(random()) % 4 + 1, 1) ||
        substr(hex(randomblob(2)), 2) || '-' ||
        hex(randomblob(6))), -- Generate UUID for ItemID
      i.id, -- ItemNumber from old id
      i.name, -- Name
      COALESCE(cm.new_id, 'dc81bc74-9002-4214-833a-c0708ab8c6be'), -- CategoryID (with default if not found)
      'PRODUCT', -- ItemType
      'PCE', -- BaseUnit (always PCE as required)
      CASE WHEN i.tax > 0 THEN 'S' ELSE 'Z' END, -- TaxID
      NULL, -- ExemptionReasonCodeID
      i.active, -- IsActive
      '${timestamp}', -- CreatedAt
      '${timestamp}', -- UpdatedAt
      'migration', -- CreatedBy
      'migration' -- UpdatedBy
    FROM old_db.items i
    LEFT JOIN category_mapping cm ON i.category_id = cm.old_id
  `;

  await run(db, insertItemsSql);
  console.log(`Migrated ${itemCount} items.`);

  return itemCount;
}

/**
 * Migrate item units from old database to new database
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} itemCount - Number of items migrated
 * @returns {Promise<void>}
 */
async function migrateItemUnits(db, timestamp, itemCount) {
  console.log('Migrating item units...');

  // Migrate all item units at once with a single SQL statement
  const insertItemUnitsSql = `
    INSERT INTO ItemUnits (
      ItemUnitID, ItemID, UnitID, ConversionFactor,
      Price, TaxedPrice, CreatedAt, UpdatedAt,
      CreatedBy, UpdatedBy
    )
    SELECT
      lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-4' ||
        substr(hex(randomblob(2)), 2) || '-' ||
        substr('89ab', abs(random()) % 4 + 1, 1) ||
        substr(hex(randomblob(2)), 2) || '-' ||
        hex(randomblob(6))), -- Generate UUID for ItemUnitID
      i.ItemID, -- ItemID
      'PCE', -- UnitID (always PCE as required)
      1, -- ConversionFactor
      ROUND(COALESCE(oi.selling_price, 0), 2), -- Price rounded to 2 decimals
      ROUND(COALESCE(oi.taxed_selling_price, oi.selling_price * (1 + COALESCE(oi.tax, 0) / 100)), 2), -- TaxedPrice from DB or calculated and rounded to 2 decimals
      '${timestamp}', -- CreatedAt
      '${timestamp}', -- UpdatedAt
      'migration', -- CreatedBy
      'migration' -- UpdatedBy
    FROM Items i
    JOIN old_db.items oi ON i.ItemNumber = oi.id
  `;

  await run(db, insertItemUnitsSql);
  console.log(`Migrated ${itemCount} item units.`);
}

/**
 * Migrate barcodes from old database to new database
 * @param {object} db - Database connection
 * @param {string} timestamp - Timestamp for all records
 * @param {number} itemCount - Number of items migrated
 * @returns {Promise<void>}
 */
async function migrateBarcodes(db, timestamp, itemCount) {
  console.log('Migrating barcodes...');

  // Migrate all barcodes at once with a single SQL statement
  const insertBarcodesSql = `
    INSERT INTO Barcodes (
      BarcodeID, ItemID, Barcode, UnitID,
      CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
    )
    SELECT
      lower(hex(randomblob(4)) || '-' || hex(randomblob(2)) || '-4' ||
        substr(hex(randomblob(2)), 2) || '-' ||
        substr('89ab', abs(random()) % 4 + 1, 1) ||
        substr(hex(randomblob(2)), 2) || '-' ||
        hex(randomblob(6))), -- Generate UUID for BarcodeID
      i.ItemID, -- ItemID
      'ABC' || substr('0000000' || i.ItemNumber, -7, 7), -- Barcode (format: ABC0000xxx)
      'PCE', -- UnitID (always PCE as required)
      '${timestamp}', -- CreatedAt
      '${timestamp}', -- UpdatedAt
      'migration', -- CreatedBy
      'migration' -- UpdatedBy
    FROM Items i
  `;

  await run(db, insertBarcodesSql);
  console.log(`Migrated ${itemCount} barcodes.`);
}

/**
 * Main function to migrate items from old database to new database
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateItems(db, startTime, callback) {
  try {
    // Step 1: Clear all related tables
    await clearTables(db);

    // Generate timestamp for all records
    const timestamp = new Date().toISOString();

    // Step 2: Migrate categories
    await migrateCategories(db, timestamp);

    // Step 3: Migrate items
    const itemCount = await migrateItemsTable(db, timestamp);

    // Step 4: Migrate item units
    await migrateItemUnits(db, timestamp, itemCount);

    // Step 5: Migrate barcodes
    await migrateBarcodes(db, timestamp, itemCount);

    // Clean up temporary table
    await run(db, 'DROP TABLE IF EXISTS category_mapping');

    // Log performance metrics
    const endTime = performance.now();
    console.log(`Migration completed in ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`Migrated ${itemCount} items with their units and barcodes.`);

    callback(null);
  } catch (err) {
    callback(err);
  }
}

module.exports = { migrateItems };
