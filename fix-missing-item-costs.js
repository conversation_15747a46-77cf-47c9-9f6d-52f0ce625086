/**
 * Fix Missing Item Costs - Final Step
 *
 * This module ensures every item has at least one inventory transaction
 * with cost information from the old database. This runs as the final step
 * after all other inventory transactions (sales, purchases, returns) have been processed.
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Add initial quantities and costs for all items as the final step
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function addInitialQuantitiesAndCosts(db, batchSize = 1000) {
  console.log('Adding initial quantities and costs for all items (final step)...');

  // Get total count of all items
  const countResult = await get(db, `
    SELECT COUNT(*) as count FROM Items
  `);
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} items to process for initial quantities and costs.`);

  if (totalItems === 0) {
    console.log('No items to process.');
    return;
  }

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get all items with their data from old database and calculate initial quantities
    const items = await all(db, `
      SELECT
        i.ItemID,
        i.ItemNumber,
        i.Name,
        old.purchase_price,
        old.selling_price,
        old.quantity as old_quantity,
        COALESCE(
          (SELECT SUM(QuantityChange)
           FROM InventoryTransactions it
           WHERE it.ItemID = i.ItemID), 0
        ) as current_transaction_sum
      FROM Items i
      JOIN old_db.items old ON i.ItemNumber = old.id
      ORDER BY i.ItemNumber
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let initialQuantityValues = [];

    for (const item of items) {
      // Calculate initial quantity needed to match old database quantity
      const initialQuantity = (item.old_quantity || 0) - item.current_transaction_sum;

      // Use purchase price from old database, fallback to selling price, then minimal cost
      const cost = item.purchase_price || item.selling_price || 0.01;

      // Only add transaction if there's an initial quantity or if item has no cost records
      const hasNoCostRecords = item.current_transaction_sum === 0;

      if (initialQuantity !== 0 || hasNoCostRecords) {
        const transactionId = uuidv4();
        const finalQuantity = hasNoCostRecords && initialQuantity === 0 ? 0 : initialQuantity;

        initialQuantityValues.push(`(
          '${transactionId}',
          '${item.ItemID}',
          'INITIAL_QUANTITY',
          ${finalQuantity},
          ${cost},
          '2020-07-01 08:27:59',
          'كمية وتكلفة أولية: ${item.Name} - كمية ${finalQuantity}, تكلفة ${cost.toFixed(4)} (الخطوة الأخيرة)',
          'migration'
        )`);
      }
    }

    // Insert initial quantity and cost transactions
    if (initialQuantityValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${initialQuantityValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created initial quantity and cost transactions for items that needed them.`);
}

/**
 * Check and report items with missing costs and quantity status
 * @param {object} db - Database connection
 */
async function checkQuantityAndCostStatus(db) {
  console.log('Checking quantity and cost status...');

  // Get count of items with no inventory transactions
  const noTransactionsResult = await get(db, `
    SELECT COUNT(*) as count
    FROM Items i
    WHERE NOT EXISTS (
      SELECT 1 FROM InventoryTransactions it
      WHERE it.ItemID = i.ItemID
    )
  `);

  // Get count of items with transactions but no cost
  const noCostResult = await get(db, `
    SELECT COUNT(DISTINCT i.ItemID) as count
    FROM Items i
    JOIN InventoryTransactions it ON i.ItemID = it.ItemID
    WHERE it.UnitCost = 0 OR it.UnitCost IS NULL
  `);

  // Get count of items with valid costs
  const withCostResult = await get(db, `
    SELECT COUNT(DISTINCT i.ItemID) as count
    FROM Items i
    JOIN InventoryTransactions it ON i.ItemID = it.ItemID
    WHERE it.UnitCost > 0
  `);

  // Get total items count
  const totalItemsResult = await get(db, 'SELECT COUNT(*) as count FROM Items');

  // Check quantity accuracy
  const quantityCheckResult = await all(db, `
    SELECT
      COUNT(*) as items_checked,
      SUM(CASE WHEN ABS(calculated_quantity - old_quantity) < 0.01 THEN 1 ELSE 0 END) as accurate_quantities,
      SUM(CASE WHEN ABS(calculated_quantity - old_quantity) >= 0.01 THEN 1 ELSE 0 END) as inaccurate_quantities
    FROM (
      SELECT
        i.ItemID,
        old.quantity as old_quantity,
        COALESCE(SUM(it.QuantityChange), 0) as calculated_quantity
      FROM Items i
      JOIN old_db.items old ON i.ItemNumber = old.id
      LEFT JOIN InventoryTransactions it ON i.ItemID = it.ItemID
      GROUP BY i.ItemID, old.quantity
    )
  `);

  console.log('\n📊 Item Status Report:');
  console.log(`Total Items: ${totalItemsResult.count}`);
  console.log(`Items with no transactions: ${noTransactionsResult.count}`);
  console.log(`Items with transactions but no cost: ${noCostResult.count}`);
  console.log(`Items with valid costs: ${withCostResult.count}`);

  if (quantityCheckResult.length > 0) {
    const qCheck = quantityCheckResult[0];
    console.log(`\n📦 Quantity Accuracy:`);
    console.log(`Items checked: ${qCheck.items_checked}`);
    console.log(`Accurate quantities: ${qCheck.accurate_quantities}`);
    console.log(`Inaccurate quantities: ${qCheck.inaccurate_quantities}`);
  }

  // Sample items with quantity discrepancies
  const discrepancySample = await all(db, `
    SELECT
      i.ItemNumber,
      i.Name,
      old.quantity as old_quantity,
      COALESCE(SUM(it.QuantityChange), 0) as calculated_quantity,
      (old.quantity - COALESCE(SUM(it.QuantityChange), 0)) as difference
    FROM Items i
    JOIN old_db.items old ON i.ItemNumber = old.id
    LEFT JOIN InventoryTransactions it ON i.ItemID = it.ItemID
    GROUP BY i.ItemID, i.ItemNumber, i.Name, old.quantity
    HAVING ABS(old.quantity - COALESCE(SUM(it.QuantityChange), 0)) >= 0.01
    ORDER BY ABS(difference) DESC
    LIMIT 10
  `);

  if (discrepancySample.length > 0) {
    console.log('\n⚠️ Sample Items with Quantity Discrepancies:');
    console.table(discrepancySample);
  }
}

/**
 * Main function to add initial quantities and costs (final step)
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function fixMissingItemCostsMain(db, startTime, callback) {
  try {
    console.log('Starting initial quantities and costs addition (final step)...');

    // Check current status
    await checkQuantityAndCostStatus(db);

    // Add initial quantities and costs
    await addInitialQuantitiesAndCosts(db);

    // Check status after fix
    console.log('\nAfter adding initial quantities and costs:');
    await checkQuantityAndCostStatus(db);

    const endTime = performance.now();
    console.log(`Initial quantities and costs addition completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error adding initial quantities and costs: ${err.message}`);
    callback(err);
  }
}

module.exports = { fixMissingItemCostsMain };
