/**
 * Supplier Transactions Migration Module
 *
 * This module creates transactions for purchase orders and purchase returns.
 * It follows the same pattern as the customer transactions migration.
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create accounts for suppliers if they don't exist
 * @param {object} db - Database connection
 */
async function createSupplierAccounts(db) {
  console.log('Creating accounts for suppliers...');

  // Get all suppliers
  const suppliers = await all(db, 'SELECT SupplierID, Name FROM Suppliers');
  console.log(`Found ${suppliers.length} suppliers.`);

  // Check which suppliers already have accounts
  const existingAccounts = await all(db, `
    SELECT AccountID FROM Accounts
    WHERE ParentID = '2100' AND AccountID LIKE 'S-%'
  `);
  const existingAccountIds = new Set(existingAccounts.map(a => a.AccountID));
  console.log(`Found ${existingAccountIds.size} existing supplier accounts.`);

  // Create accounts for suppliers that don't have one
  let accountValues = [];
  for (const supplier of suppliers) {
    if (!existingAccountIds.has(supplier.SupplierID)) {
      accountValues.push(`(
        '${supplier.SupplierID}',
        '${supplier.Name}',
        '2100',
        'Liability',
        'Credit',
        1,
        'Supplier account for ${supplier.Name}',
        0.0
      )`);
    }
  }

  if (accountValues.length > 0) {
    await run(db, `
      INSERT INTO Accounts (
        AccountID, AccountName, ParentID,
        AccountType, NormalBalance, IsLeaf,
        Description, Balance
      ) VALUES ${accountValues.join(',')}
    `);
    console.log(`Created ${accountValues.length} new supplier accounts.`);
  } else {
    console.log('No new supplier accounts needed.');
  }
}

/**
 * Create transactions for purchase orders
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseTransactions(db, batchSize = 10000) {
  console.log('Creating transactions for purchase orders...');

  // Get total count of purchase orders
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrders');
  const totalOrders = countResult.count;
  console.log(`Found ${totalOrders} purchase orders to process.`);

  // Process in batches
  const batches = Math.ceil(totalOrders / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} orders each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase orders with non-zero amounts
    const orders = await all(db, `
      SELECT OrderID, SupplierID, InvoiceTotalAmount, OrderDate, PurchaseOrderNumber, InvoiceNumber
      FROM PurchaseOrders
      WHERE InvoiceTotalAmount > 0
      ORDER BY OrderID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) {
      console.log(`No orders found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${orders.length} orders in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const order of orders) {
      // Create a transaction for the purchase
      const transactionId = uuidv4();

      // For purchases on credit (most purchases are on credit):
      // Debit: Purchases (5100) - expense increases
      // Credit: Supplier Account (Liability increases)
      transactionValues.push(`(
        '${transactionId}',
        '${order.OrderDate}',
        'Credit Purchase: ${order.InvoiceNumber}',
        '5100',
        '${order.SupplierID}',
        ${order.InvoiceTotalAmount},
        '${order.OrderID}',
        'PurchaseCredit',
        'migration',
        '${new Date().toISOString()}'
      )`);
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} purchase orders.`);
}

/**
 * Create transactions for purchase returns
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseReturnTransactions(db, batchSize = 10000) {
  console.log('Creating transactions for purchase returns...');

  // Get total count of purchase order returns
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderReturns');
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} purchase order returns to process.`);

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} returns each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order returns with supplier ID and non-zero amounts
    const returns = await all(db, `
      SELECT r.ReturnID, r.OrderID, r.InvoiceTotalAmount as ReturnAmount, r.ReturnDate, p.SupplierID,
             r.ReturnNumber, r.FinalInvoiceNumber, p.InvoiceNumber
      FROM PurchaseOrderReturns r
      JOIN PurchaseOrders p ON r.OrderID = p.OrderID
      WHERE r.InvoiceTotalAmount > 0
      ORDER BY r.ReturnID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) {
      console.log(`No returns found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${returns.length} returns in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const returnItem of returns) {
      // Create a transaction for the return
      const transactionId = uuidv4();

      // For purchase returns (returning credit purchases):
      // Debit: Supplier Account (Liability decreases)
      // Credit: Purchases (5100) - expense decreases
      transactionValues.push(`(
        '${transactionId}',
        '${returnItem.ReturnDate}',
        'Credit Purchase Return: ${returnItem.FinalInvoiceNumber} for invoice ${returnItem.InvoiceNumber}',
        '${returnItem.SupplierID}',
        '5100',
        ${returnItem.ReturnAmount},
        '${returnItem.OrderID}',
        'ReturnCreditPurchase',
        'migration',
        '${new Date().toISOString()}'
      )`);
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} purchase order returns.`);
}

/**
 * Main function to migrate supplier transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateSupplierTransactions(db, startTime, callback) {
  try {
    console.log('Starting supplier transactions migration...');

    // Clear existing transactions for purchases and returns
    console.log('Clearing existing purchase and return transactions...');
    await run(db, "DELETE FROM Transactions WHERE ReferenceType IN ('PurchaseOrder', 'PurchaseOrderReturn', 'PurchaseCredit', 'ReturnCreditPurchase')");

    // Create accounts for suppliers
    await createSupplierAccounts(db);

    // Create transactions for purchases
    await createPurchaseTransactions(db);

    // Create transactions for returns
    await createPurchaseReturnTransactions(db);

    const endTime = performance.now();
    console.log(`Supplier transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating supplier transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateSupplierTransactions };
