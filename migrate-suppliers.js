/**
 * Supplier migration module
 *
 * This module focuses solely on migrating suppliers from the old database to the new database.
 * It is called by migrator.js which handles the database connections and transactions.
 */

const { performance } = require('perf_hooks');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

/**
 * Migrate suppliers from old database to new database
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateSuppliers(db, startTime, callback) {
  try {
    // Step 1: Remove all suppliers
    console.log('Removing existing suppliers...');
    await run(db, "DELETE FROM Suppliers");
    console.log('Existing suppliers removed successfully.');

    // Get count of suppliers to migrate
    const result = await get(db, 'SELECT COUNT(*) as count FROM old_db.suppliers');
    const supplierCount = result.count;
    console.log(`Found ${supplierCount} suppliers to migrate.`);

    // Generate timestamp for all records
    const now = new Date().toISOString();

    // Step 2: Direct insert from old database to new database
    console.log('Performing direct insert...');
    const insertSql = `
      INSERT INTO Suppliers (
        SupplierID, Name, Phone, Email, Address, VATNumber,
        BuildingNumber, StreetName, District, City, Country,
        PostalCode, AdditionalNumber, OtherID, ContactPerson,
        Notes, CreatedAt, UpdatedAt, CreatedBy, UpdatedBy
      )
      SELECT
        'S-' || id,
        full_name,
        COALESCE(mobile, phone, ''),
        '',
        COALESCE(address, ''),
        '',
        '',
        '',
        '',
        '',
        'SA',
        '',
        NULL,
        id,
        NULL,
        'Migrated from old database. Credit: ' || COALESCE(total_credit, 0),
        '${now}',
        '${now}',
        'migration',
        'migration'
      FROM old_db.suppliers
    `;

    // Execute the insert
    await run(db, insertSql);

    // Log performance metrics
    const endTime = performance.now();
    console.log(`Migration completed in ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`Migrated ${supplierCount} suppliers`);

    // Call the callback with no error
    callback(null);
  } catch (err) {
    callback(err);
  }
}

module.exports = { migrateSuppliers };
