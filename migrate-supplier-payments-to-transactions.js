/**
 * Supplier Payments to Transactions Migration Module
 *
 * This module migrates supplier payments directly from the old database's purchase_order_payments table
 * to the new database's Transactions table, bypassing the Payments table entirely.
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create transactions for supplier payments directly from old database
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSupplierPaymentTransactions(db, batchSize = 10000) {
  console.log('Creating transactions for supplier payments directly from old database...');

  // Get total count of payments to migrate
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.purchase_order_payments p
    JOIN old_db.purchase_orders o ON p.purchase_order_id = o.id
    WHERE o.confirmed = 1
  `);
  const totalPayments = countResult.count;
  console.log(`Found ${totalPayments} supplier payments to process.`);

  // Process in batches
  const batches = Math.ceil(totalPayments / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} payments each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of payments with purchase order information
    const payments = await all(db, `
      SELECT
        p.id as payment_id,
        p.purchase_order_id as old_order_id,
        p.amount,
        p.payment_date,
        o.supplier_id as old_supplier_id,
        o.transaction_date as order_date,
        om.new_id as new_order_id,
        po.InvoiceNumber
      FROM old_db.purchase_order_payments p
      JOIN old_db.purchase_orders o ON p.purchase_order_id = o.id
      JOIN purchase_order_mapping om ON p.purchase_order_id = om.old_id
      JOIN PurchaseOrders po ON om.new_id = po.OrderID
      WHERE o.confirmed = 1
      ORDER BY p.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (payments.length === 0) {
      console.log(`No payments found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${payments.length} payments in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const payment of payments) {
      // Create a transaction for the payment
      const transactionId = uuidv4();
      const amount = Math.abs(payment.amount); // Use absolute value for the transaction amount
      const cashAccountId = '1100'; // Cash account ID
      const now = new Date().toISOString();

      // Get supplier ID from the new database
      const supplier = await get(db, `
        SELECT SupplierID
        FROM PurchaseOrders
        WHERE OrderID = ?
      `, [payment.new_order_id]);

      const supplierID = supplier ? supplier.SupplierID : null;

      if (!supplierID) {
        console.log(`Warning: Could not find supplier for order ${payment.new_order_id}. Skipping payment.`);
        continue;
      }

      if (payment.amount >= 0) {
        // Positive amount: We are paying the supplier
        // Debit: Supplier Account (Liability decreases)
        // Credit: Cash/Bank Account (Asset decreases)
        transactionValues.push(`(
          '${transactionId}',
          '${payment.payment_date}',
          'Payment for invoice ${payment.InvoiceNumber}',
          '${supplierID}',
          '${cashAccountId}',
          ${amount},
          '${payment.new_order_id}',
          'SupplierPayment',
          'migration',
          '${now}'
        )`);
      } else {
        // Negative amount: Supplier is refunding money back to us
        // Debit: Cash/Bank Account (Asset increases)
        // Credit: Supplier Account (Liability increases)
        transactionValues.push(`(
          '${transactionId}',
          '${payment.payment_date}',
          'Refund from supplier for invoice ${payment.InvoiceNumber}',
          '${cashAccountId}',
          '${supplierID}',
          ${amount},
          '${payment.new_order_id}',
          'SupplierRefund',
          'migration',
          '${now}'
        )`);
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += payments.length;
    console.log(`Processed ${totalProcessed}/${totalPayments} payments (${Math.round(totalProcessed/totalPayments*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} supplier payments.`);
}

/**
 * Create and populate purchase order mapping table if needed
 * @param {object} db - Database connection
 */
async function createPurchaseOrderMappingTable(db) {
  console.log('Creating purchase order mapping table if needed...');

  // Create temporary mapping table if it doesn't exist
  await run(db, `
    CREATE TEMPORARY TABLE IF NOT EXISTS purchase_order_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Create index if it doesn't exist
  await run(db, `CREATE INDEX IF NOT EXISTS idx_purchase_order_mapping_new_id ON purchase_order_mapping(new_id)`);

  // Check if the table is empty
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM purchase_order_mapping');

  if (countResult.count === 0) {
    console.log('Populating purchase order mapping table...');
    await run(db, `
      INSERT INTO purchase_order_mapping (old_id, new_id)
      SELECT o.id, p.OrderID
      FROM old_db.purchase_orders o
      JOIN PurchaseOrders p ON 'PO-' || printf('%06d', o.id) = p.PurchaseOrderNumber
      WHERE o.confirmed = 1
    `);
    console.log('Purchase order mapping table populated successfully.');
  } else {
    console.log(`Purchase order mapping table already has ${countResult.count} entries.`);
  }
}

/**
 * Main function to migrate supplier payments directly to transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateSupplierPaymentsToTransactions(db, startTime, callback) {
  try {
    console.log('Starting supplier payments to transactions migration...');

    // Create and populate purchase order mapping table if needed
    await createPurchaseOrderMappingTable(db);

    // Clear existing supplier payment transactions
    console.log('Clearing existing supplier payment transactions...');
    await run(db, "DELETE FROM Transactions WHERE ReferenceType IN ('SupplierPayment', 'SupplierRefund')");

    // Create transactions for supplier payments
    await createSupplierPaymentTransactions(db);

    const endTime = performance.now();
    console.log(`Supplier payments to transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating supplier payments to transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateSupplierPaymentsToTransactions };
