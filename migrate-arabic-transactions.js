/**
 * Arabic Transactions Migration Module
 *
 * This module creates transactions using the proper Arabic chart of accounts
 * and places customers under 1121 (العملاء) and suppliers under 2110 (الموردون)
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Clear all existing transactions and inventory transactions
 * @param {object} db - Database connection
 */
async function clearAllTransactions(db) {
  console.log('Clearing all existing transactions and inventory transactions...');

  // Clear all transactions
  await run(db, 'DELETE FROM Transactions');
  console.log('All transactions cleared.');

  // Clear all inventory transactions
  await run(db, 'DELETE FROM InventoryTransactions');
  console.log('All inventory transactions cleared.');

  // Clear customer accounts under 1121 and 1122
  await run(db, "DELETE FROM Accounts WHERE ParentID = '1121'");
  await run(db, "DELETE FROM Accounts WHERE ParentID = '1122'");
  console.log('Customer accounts cleared.');

  // Clear supplier accounts under 2110 and 2120
  await run(db, "DELETE FROM Accounts WHERE ParentID = '2110'");
  await run(db, "DELETE FROM Accounts WHERE ParentID = '2120'");
  console.log('Supplier accounts cleared.');
}

/**
 * Create customer accounts under 1121 (العملاء) and 1122 (دفعات مقدمة من العملاء)
 * @param {object} db - Database connection
 */
async function createCustomerAccounts(db) {
  console.log('Creating customer accounts under 1121 (العملاء) and 1122 (دفعات مقدمة من العملاء)...');

  // Get all customers
  const customers = await all(db, 'SELECT CustomerID, Name FROM Customers');
  console.log(`Found ${customers.length} customers to create accounts for.`);

  // Prepare batch insert for accounts
  let accountValues = [];

  for (const customer of customers) {
    // Use the actual CustomerID from new database (UUID format)
    const customerID = customer.CustomerID;

    // Add normal customer account under 1121 (no prefix, use UUID)
    accountValues.push(`(
      '${customerID}',
      '${(customer.Name || 'Customer Account').replace(/'/g, "''")}',
      '1121',
      'Asset',
      'Debit',
      1,
      'Normal customer account for ${(customer.Name || 'Customer').replace(/'/g, "''")}'
    )`);

    // Add advance customer account under 1122 (with adv- prefix, use UUID)
    accountValues.push(`(
      'adv-${customerID}',
      'دفعة مقدمة - ${(customer.Name || 'Customer Account').replace(/'/g, "''")}',
      '1122',
      'Liability',
      'Credit',
      1,
      'Advance payment account for ${(customer.Name || 'Customer').replace(/'/g, "''")}'
    )`);
  }

  // Insert accounts
  if (accountValues.length > 0) {
    await run(db, `
      INSERT INTO Accounts (
        AccountID, AccountName, ParentID, AccountType, NormalBalance, IsLeaf, Description
      ) VALUES ${accountValues.join(',')}
    `);
  }

  console.log(`Created ${customers.length * 2} customer accounts (normal + advance) under 1121 and 1122.`);
}

/**
 * Create supplier accounts under 2110 (الموردون) and 2120 (دفعات مقدمة للموردين)
 * @param {object} db - Database connection
 */
async function createSupplierAccounts(db) {
  console.log('Creating supplier accounts under 2110 (الموردون) and 2120 (دفعات مقدمة للموردين)...');

  // Get all suppliers
  const suppliers = await all(db, 'SELECT SupplierID, Name FROM Suppliers');
  console.log(`Found ${suppliers.length} suppliers to create accounts for.`);

  // Prepare batch insert for accounts
  let accountValues = [];

  for (const supplier of suppliers) {
    // Use the actual SupplierID from new database (UUID format)
    const supplierID = supplier.SupplierID;

    // Add normal supplier account under 2110 (no prefix, use UUID)
    accountValues.push(`(
      '${supplierID}',
      '${(supplier.Name || 'Supplier Account').replace(/'/g, "''")}',
      '2110',
      'Liability',
      'Credit',
      1,
      'Normal supplier account for ${(supplier.Name || 'Supplier').replace(/'/g, "''")}'
    )`);

    // Add advance supplier account under 2120 (with adv- prefix, use UUID)
    accountValues.push(`(
      'adv-${supplierID}',
      'دفعة مقدمة - ${(supplier.Name || 'Supplier Account').replace(/'/g, "''")}',
      '2120',
      'Asset',
      'Debit',
      1,
      'Advance payment account for ${(supplier.Name || 'Supplier').replace(/'/g, "''")}'
    )`);
  }

  // Insert accounts
  if (accountValues.length > 0) {
    await run(db, `
      INSERT INTO Accounts (
        AccountID, AccountName, ParentID, AccountType, NormalBalance, IsLeaf, Description
      ) VALUES ${accountValues.join(',')}
    `);
  }

  console.log(`Created ${suppliers.length * 2} supplier accounts (normal + advance) under 2110 and 2120.`);
}

/**
 * Create sales transactions using proper Arabic accounts
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesTransactions(db, batchSize = 10000) {
  console.log('Creating sales transactions using Arabic chart of accounts...');

  // Get total count of sales orders
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrders');
  const totalOrders = countResult.count;
  console.log(`Found ${totalOrders} sales orders to process.`);

  // Process in batches
  const batches = Math.ceil(totalOrders / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} orders each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales orders
    const orders = await all(db, `
      SELECT OrderID, CustomerID, InvoiceTotalAmount, OrderDate, SalesOrderNumber, InvoiceNumber
      FROM SalesOrders
      ORDER BY OrderID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) {
      console.log(`No orders found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${orders.length} orders in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const order of orders) {
      // Create a transaction for the sale
      const transactionId = uuidv4();

      if (order.CustomerID === 'C-UNKNOWN') {
        // Cash sale: Debit 1111 (الصندوق), Credit 4110 (مبيعات نقداً)
        transactionValues.push(`(
          '${transactionId}',
          '${order.OrderDate}',
          'مبيعات نقداً - ${order.InvoiceNumber}',
          '1111',
          '4110',
          ${parseFloat(order.InvoiceTotalAmount).toFixed(2)},
          '${order.OrderID}',
          'SALE',
          'migration',
          '${new Date().toISOString()}'
        )`);
      } else {
        // Credit sale: Debit Customer Account (use UUID), Credit 4120 (مبيعات بالآجل)
        transactionValues.push(`(
          '${transactionId}',
          '${order.OrderDate}',
          'مبيعات بالآجل - ${order.InvoiceNumber}',
          '${order.CustomerID}',
          '4120',
          ${parseFloat(order.InvoiceTotalAmount).toFixed(2)},
          '${order.OrderID}',
          'SALE',
          'migration',
          '${new Date().toISOString()}'
        )`);
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} sales orders.`);
}

/**
 * Create sales return transactions using proper Arabic accounts
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSalesReturnTransactions(db, batchSize = 10000) {
  console.log('Creating sales return transactions using Arabic chart of accounts...');

  // Get total count of sales order returns
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM SalesOrderReturns');
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} sales order returns to process.`);

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} returns each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of sales order returns with customer ID
    const returns = await all(db, `
      SELECT r.ReturnID, r.OrderID, r.InvoiceTotalAmount as ReturnAmount, r.ReturnDate, s.CustomerID,
             r.ReturnNumber, r.FinalInvoiceNumber, s.InvoiceNumber
      FROM SalesOrderReturns r
      JOIN SalesOrders s ON r.OrderID = s.OrderID
      ORDER BY r.ReturnID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) {
      console.log(`No returns found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${returns.length} returns in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const returnItem of returns) {
      // Create a transaction for the return
      const transactionId = uuidv4();

      if (returnItem.CustomerID === 'C-UNKNOWN') {
        // Cash sale return: Debit 4210 (مرتجع مبيعات نقداً), Credit 1111 (الصندوق)
        transactionValues.push(`(
          '${transactionId}',
          '${returnItem.ReturnDate}',
          'مرتجع مبيعات نقداً - ${returnItem.FinalInvoiceNumber}',
          '4210',
          '1111',
          ${parseFloat(returnItem.ReturnAmount).toFixed(2)},
          '${returnItem.OrderID}',
          'RETURN',
          'migration',
          '${new Date().toISOString()}'
        )`);
      } else {
        // Credit sale return: Debit 4220 (مرتجع مبيعات بالآجل), Credit Customer Account (use UUID)
        transactionValues.push(`(
          '${transactionId}',
          '${returnItem.ReturnDate}',
          'مرتجع مبيعات بالآجل - ${returnItem.FinalInvoiceNumber}',
          '4220',
          '${returnItem.CustomerID}',
          ${parseFloat(returnItem.ReturnAmount).toFixed(2)},
          '${returnItem.OrderID}',
          'RETURN',
          'migration',
          '${new Date().toISOString()}'
        )`);
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} sales order returns.`);
}

/**
 * Conservative rounding function - ONLY round to 2 decimals, do NOT change payment amounts
 * @param {number} amount - Original amount
 * @returns {number} - Rounded amount to 2 decimals
 */
function conservativeRoundAmount(amount) {
  // ONLY round to 2 decimal places, do NOT modify the actual payment amount
  return parseFloat(amount).toFixed(2);
}

/**
 * Create customer payment transactions using proper Arabic accounts
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createCustomerPaymentTransactions(db, batchSize = 10000) {
  console.log('Creating customer payment transactions using Arabic chart of accounts...');

  // Create and populate order mapping table if needed
  await run(db, `
    CREATE TEMPORARY TABLE IF NOT EXISTS order_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Check if the table is empty and populate it
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM order_mapping');
  if (countResult.count === 0) {
    await run(db, `
      INSERT INTO order_mapping (old_id, new_id)
      SELECT o.id, s.OrderID
      FROM old_db.sales_orders o
      JOIN SalesOrders s ON 'SO-' || printf('%06d', o.id) = s.SalesOrderNumber
      WHERE o.confirmed = 1
    `);
  }

  // Get total count of payments for known customers grouped by order
  const totalResult = await get(db, `
    SELECT COUNT(DISTINCT p.sales_order_id) as count
    FROM old_db.sales_order_payments p
    JOIN old_db.sales_orders o ON p.sales_order_id = o.id
    WHERE o.confirmed = 1 AND o.customer_id IS NOT NULL
  `);
  const totalOrders = totalResult.count;
  console.log(`Found ${totalOrders} orders with customer payments to process.`);

  // Process payments grouped by order to enable smart rounding
  const batches = Math.ceil(totalOrders / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of orders with their payments
    const orders = await all(db, `
      SELECT DISTINCT
        p.sales_order_id,
        om.new_id as new_order_id,
        s.InvoiceNumber,
        s.CustomerID,
        s.TotalAfterReturns
      FROM old_db.sales_order_payments p
      JOIN old_db.sales_orders o ON p.sales_order_id = o.id
      JOIN order_mapping om ON p.sales_order_id = om.old_id
      JOIN SalesOrders s ON om.new_id = s.OrderID
      WHERE o.confirmed = 1 AND o.customer_id IS NOT NULL
      ORDER BY p.sales_order_id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) continue;

    let transactionValues = [];

    for (const order of orders) {
      // Get all payments for this order
      const payments = await all(db, `
        SELECT
          p.id as payment_id,
          p.amount,
          p.payment_date
        FROM old_db.sales_order_payments p
        JOIN old_db.sales_orders o ON p.sales_order_id = o.id
        WHERE o.confirmed = 1 AND o.customer_id IS NOT NULL AND p.sales_order_id = ${order.sales_order_id}
        ORDER BY p.id
      `);

      let currentPaid = 0;
      const totalAmount = parseFloat(order.TotalAfterReturns || 0);

      for (let i = 0; i < payments.length; i++) {
        const payment = payments[i];
        const transactionId = uuidv4();
        let amount = Math.abs(payment.amount);

        // Apply conservative rounding - ONLY round to 2 decimals
        amount = conservativeRoundAmount(amount);

        if (payment.amount >= 0) {
          // Customer payment: Debit 1111 (الصندوق), Credit Customer Account (use UUID)
          transactionValues.push(`(
            '${transactionId}',
            '${payment.payment_date}',
            'تحصيل من العميل - ${order.InvoiceNumber}',
            '1111',
            '${order.CustomerID}',
            ${amount},
            '${order.new_order_id}',
            'PAYMENT',
            'migration',
            '${new Date().toISOString()}'
          )`);
          currentPaid += parseFloat(amount);
        } else {
          // Customer refund: Debit Customer Account (use UUID), Credit 1111 (الصندوق)
          transactionValues.push(`(
            '${transactionId}',
            '${payment.payment_date}',
            'رد للعميل - ${order.InvoiceNumber}',
            '${order.CustomerID}',
            '1111',
            ${amount},
            '${order.new_order_id}',
            'REFUND',
            'migration',
            '${new Date().toISOString()}'
          )`);
        }
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} customer payments.`);
}

/**
 * Create purchase transactions using proper Arabic accounts
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseTransactions(db, batchSize = 10000) {
  console.log('Creating purchase transactions using Arabic chart of accounts...');

  // Get total count of purchase orders
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrders WHERE InvoiceTotalAmount > 0');
  const totalOrders = countResult.count;
  console.log(`Found ${totalOrders} purchase orders to process.`);

  // Process in batches
  const batches = Math.ceil(totalOrders / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase orders
    const orders = await all(db, `
      SELECT OrderID, SupplierID, InvoiceTotalAmount, OrderDate, PurchaseOrderNumber, InvoiceNumber
      FROM PurchaseOrders
      WHERE InvoiceTotalAmount > 0
      ORDER BY OrderID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) continue;

    let transactionValues = [];

    for (const order of orders) {
      const transactionId = uuidv4();

      // Purchase on credit: Debit 5120 (مشتريات بالآجل), Credit Supplier Account (use UUID)
      transactionValues.push(`(
        '${transactionId}',
        '${order.OrderDate}',
        'مشتريات بالآجل - ${order.InvoiceNumber}',
        '5120',
        '${order.SupplierID}',
        ${parseFloat(order.InvoiceTotalAmount).toFixed(2)},
        '${order.OrderID}',
        'PURCHASE',
        'migration',
        '${new Date().toISOString()}'
      )`);
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} purchase orders.`);
}

/**
 * Create purchase return transactions using proper Arabic accounts
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPurchaseReturnTransactions(db, batchSize = 10000) {
  console.log('Creating purchase return transactions using Arabic chart of accounts...');

  // Get total count of purchase order returns
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM PurchaseOrderReturns WHERE InvoiceTotalAmount > 0');
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} purchase order returns to process.`);

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of purchase order returns
    const returns = await all(db, `
      SELECT r.ReturnID, r.OrderID, r.InvoiceTotalAmount as ReturnAmount, r.ReturnDate, p.SupplierID,
             r.ReturnNumber, r.FinalInvoiceNumber, p.InvoiceNumber
      FROM PurchaseOrderReturns r
      JOIN PurchaseOrders p ON r.OrderID = p.OrderID
      WHERE r.InvoiceTotalAmount > 0
      ORDER BY r.ReturnID
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) continue;

    let transactionValues = [];

    for (const returnItem of returns) {
      const transactionId = uuidv4();

      // Purchase return: Debit Supplier Account (use UUID), Credit 5220 (مرتجع مشتريات بالآجل)
      transactionValues.push(`(
        '${transactionId}',
        '${returnItem.ReturnDate}',
        'مرتجع مشتريات بالآجل - ${returnItem.FinalInvoiceNumber}',
        '${returnItem.SupplierID}',
        '5220',
        ${parseFloat(returnItem.ReturnAmount).toFixed(2)},
        '${returnItem.OrderID}',
        'PURCHASE_RETURN',
        'migration',
        '${new Date().toISOString()}'
      )`);
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} purchase order returns.`);
}

/**
 * Create supplier payment transactions using proper Arabic accounts
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createSupplierPaymentTransactions(db, batchSize = 10000) {
  console.log('Creating supplier payment transactions using Arabic chart of accounts...');

  // Create and populate purchase order mapping table if needed
  await run(db, `
    CREATE TEMPORARY TABLE IF NOT EXISTS purchase_order_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Check if the table is empty and populate it
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM purchase_order_mapping');
  if (countResult.count === 0) {
    await run(db, `
      INSERT INTO purchase_order_mapping (old_id, new_id)
      SELECT o.id, p.OrderID
      FROM old_db.purchase_orders o
      JOIN PurchaseOrders p ON 'PO-' || printf('%06d', o.id) = p.PurchaseOrderNumber
      WHERE o.confirmed = 1
    `);
  }

  // Get total count of supplier payments grouped by order
  const totalResult = await get(db, `
    SELECT COUNT(DISTINCT p.purchase_order_id) as count
    FROM old_db.purchase_order_payments p
    JOIN old_db.purchase_orders o ON p.purchase_order_id = o.id
    WHERE o.confirmed = 1
  `);
  const totalOrders = totalResult.count;
  console.log(`Found ${totalOrders} orders with supplier payments to process.`);

  // Process payments grouped by order to enable smart rounding
  const batches = Math.ceil(totalOrders / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of orders with their payments
    const orders = await all(db, `
      SELECT DISTINCT
        p.purchase_order_id,
        om.new_id as new_order_id,
        po.InvoiceNumber,
        po.SupplierID,
        po.TotalAfterReturns
      FROM old_db.purchase_order_payments p
      JOIN old_db.purchase_orders o ON p.purchase_order_id = o.id
      JOIN purchase_order_mapping om ON p.purchase_order_id = om.old_id
      JOIN PurchaseOrders po ON om.new_id = po.OrderID
      WHERE o.confirmed = 1
      ORDER BY p.purchase_order_id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (orders.length === 0) continue;

    let transactionValues = [];

    for (const order of orders) {
      // Get all payments for this order
      const payments = await all(db, `
        SELECT
          p.id as payment_id,
          p.amount,
          p.payment_date
        FROM old_db.purchase_order_payments p
        JOIN old_db.purchase_orders o ON p.purchase_order_id = o.id
        WHERE o.confirmed = 1 AND p.purchase_order_id = ${order.purchase_order_id}
        ORDER BY p.id
      `);

      let currentPaid = 0;
      const totalAmount = parseFloat(order.TotalAfterReturns || 0);

      for (let i = 0; i < payments.length; i++) {
        const payment = payments[i];
        const transactionId = uuidv4();
        let amount = Math.abs(payment.amount);

        // Apply conservative rounding - ONLY round to 2 decimals
        amount = conservativeRoundAmount(amount);

        if (payment.amount >= 0) {
          // Supplier payment: Debit Supplier Account (use UUID), Credit 1111 (الصندوق)
          transactionValues.push(`(
            '${transactionId}',
            '${payment.payment_date}',
            'دفع للمورد - ${order.InvoiceNumber}',
            '${order.SupplierID}',
            '1111',
            ${amount},
            '${order.new_order_id}',
            'SUPPLIER_PAYMENT',
            'migration',
            '${new Date().toISOString()}'
          )`);
          currentPaid += parseFloat(amount);
        } else {
          // Supplier refund: Debit 1111 (الصندوق), Credit Supplier Account (use UUID)
          transactionValues.push(`(
            '${transactionId}',
            '${payment.payment_date}',
            'رد من المورد - ${order.InvoiceNumber}',
            '1111',
            '${order.SupplierID}',
            ${amount},
            '${order.new_order_id}',
            'SUPPLIER_REFUND',
            'migration',
            '${new Date().toISOString()}'
          )`);
        }
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += orders.length;
    console.log(`Processed ${totalProcessed}/${totalOrders} orders (${Math.round(totalProcessed/totalOrders*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} supplier payments.`);
}

/**
 * Apply smart rounding to all remaining amounts after transactions are created
 * @param {object} db - Database connection
 */
async function applySmartRoundingToRemainingAmounts(db) {
  console.log('Applying smart rounding to remaining amounts...');

  // Smart rounding for sales orders with small remaining amounts
  // ONLY adjust remaining amount to 0, do NOT change paid amount
  const salesResult = await run(db, `
    UPDATE SalesOrders
    SET RemainingAmount = 0
    WHERE CustomerID IS NOT NULL
      AND ABS(RemainingAmount) > 0
      AND ABS(RemainingAmount) < 1
  `);

  // Smart rounding for purchase orders with small remaining amounts
  // ONLY adjust remaining amount to 0, do NOT change paid amount
  const purchaseResult = await run(db, `
    UPDATE PurchaseOrders
    SET RemainingAmount = 0
    WHERE ABS(RemainingAmount) > 0
      AND ABS(RemainingAmount) < 1
  `);

  console.log(`Applied smart rounding to sales orders: ${salesResult.changes || 0} updated`);
  console.log(`Applied smart rounding to purchase orders: ${purchaseResult.changes || 0} updated`);
}

/**
 * Main function to migrate Arabic transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateArabicTransactions(db, startTime, callback) {
  try {
    console.log('Starting Arabic transactions migration...');

    // Clear all existing transactions and accounts
    await clearAllTransactions(db);

    // Create customer accounts under 1121
    await createCustomerAccounts(db);

    // Create supplier accounts under 2110
    await createSupplierAccounts(db);

    // Create sales transactions
    await createSalesTransactions(db);

    // Create sales return transactions
    await createSalesReturnTransactions(db);

    // Create customer payment transactions
    await createCustomerPaymentTransactions(db);

    // Create purchase transactions
    await createPurchaseTransactions(db);

    // Create purchase return transactions
    await createPurchaseReturnTransactions(db);

    // Create supplier payment transactions
    await createSupplierPaymentTransactions(db);

    // Apply smart rounding to remaining amounts
    await applySmartRoundingToRemainingAmounts(db);

    const endTime = performance.now();
    console.log(`Arabic transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating Arabic transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateArabicTransactions };
