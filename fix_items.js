var items = [
    {
        "id": "291",
        "name": "ماسورة 3/4 إنش حار بحريني BP",
        "taxed_selling_price": "28"
    },
    {
        "id": "292",
        "name": "ماسورة 1/2 إنش أبيض",
        "taxed_selling_price": "12"
    },
    {
        "id": "293",
        "name": "ماسورة 4 إنش 4 ملي",
        "taxed_selling_price": "85"
    },
    {
        "id": "294",
        "name": "ماسورة 1 1/2 إنش بي بي بحريني",
        "taxed_selling_price": "33"
    },
    {
        "id": "295",
        "name": "ماسورة 2 إنش كهرباء (خفيف)",
        "taxed_selling_price": "27"
    },
    {
        "id": "552",
        "name": "ماسورة 3 إنش كهرباء (بحريني) BB",
        "taxed_selling_price": "32"
    },
    {
        "id": "553",
        "name": "ماسورة 1/2 إنش بارد",
        "taxed_selling_price": "14"
    },
    {
        "id": "554",
        "name": "ماسورة 3 إنش 2.5 ملي",
        "taxed_selling_price": "40"
    },
    {
        "id": "555",
        "name": "ماسورة 2 إنش حار بحريني",
        "taxed_selling_price": "105"
    },
    {
        "id": "556",
        "name": "ماسورة 1 1/2 إنش كهرباء( خفيف)",
        "taxed_selling_price": "19"
    },
    {
        "id": "557",
        "name": "ماسورة 1/2 إنش حار CHANNAL",
        "taxed_selling_price": "27"
    },
    {
        "id": "558",
        "name": "ماسورة 3/4 إنش بارد (متر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "559",
        "name": "ماسورة 1 إنش ثقيل ض 40",
        "taxed_selling_price": "25"
    },
    {
        "id": "560",
        "name": "ماسورة 3/4 إنش أبيض",
        "taxed_selling_price": "12"
    },
    {
        "id": "561",
        "name": "ماسورة 1 إنش أبيض",
        "taxed_selling_price": "16"
    },
    {
        "id": "660",
        "name": "واير مسك مقاس 12",
        "taxed_selling_price": "220"
    },
    {
        "id": "661",
        "name": "واير أمريكي مقاس 10 (رولة )",
        "taxed_selling_price": "410"
    },
    {
        "id": "662",
        "name": "واير أمريكي مقاس 12 (رولة )",
        "taxed_selling_price": "263"
    },
    {
        "id": "663",
        "name": "واير مقاس 14 (بالمتر)",
        "taxed_selling_price": "1.5"
    },
    {
        "id": "664",
        "name": "واير مقاس 10 (بالمتر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "665",
        "name": "واير مقاس 12 (بالمتر)",
        "taxed_selling_price": "2"
    },
    {
        "id": "802",
        "name": "واير 1.5*2 mm (بالمتر)",
        "taxed_selling_price": "2"
    },
    {
        "id": "803",
        "name": "واير 6*3 mm (بالمتر)",
        "taxed_selling_price": "8"
    },
    {
        "id": "804",
        "name": "واير 2.5*2 إنش M*2",
        "taxed_selling_price": "140"
    },
    {
        "id": "805",
        "name": "واير 2.5*2 mm (بالمتر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "806",
        "name": "واير 2.5*2 ملي رصاصي 80 ياردة",
        "taxed_selling_price": "155"
    },
    {
        "id": "807",
        "name": "واير 2.5*2 ملي رصاصي (بالمتر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "863",
        "name": "ماسورة 6 انش 5.2 ملي",
        "taxed_selling_price": "195"
    },
    {
        "id": "913",
        "name": "واير بروش طقم صغير",
        "taxed_selling_price": "5"
    },
    {
        "id": "914",
        "name": "واير بروش يدية بلاستيك أحمر",
        "taxed_selling_price": "5"
    },
    {
        "id": "915",
        "name": "واير بروش يدية خشب",
        "taxed_selling_price": "5"
    },
    {
        "id": "1152",
        "name": "واير سينار عريض (بالمتر)",
        "taxed_selling_price": "1.5"
    },
    {
        "id": "1172",
        "name": "كيبل 35 ملي الرياض",
        "taxed_selling_price": "33"
    },
    {
        "id": "1174",
        "name": "واير جرس أبيض",
        "taxed_selling_price": "30"
    },
    {
        "id": "1175",
        "name": "واير جرس رصاصي",
        "taxed_selling_price": "30"
    },
    {
        "id": "1176",
        "name": "واير تلفون 1 خط",
        "taxed_selling_price": "15"
    },
    {
        "id": "1177",
        "name": "واير تلفون 2 خط",
        "taxed_selling_price": "15"
    },
    {
        "id": "1178",
        "name": "واير تلفون 3 خط",
        "taxed_selling_price": "25"
    },
    {
        "id": "1179",
        "name": "واير تلفون 10باير امريكي",
        "taxed_selling_price": "395"
    },
    {
        "id": "1180",
        "name": "واير تلفون 5 خط",
        "taxed_selling_price": "30"
    },
    {
        "id": "1181",
        "name": "واير تلفون 6 خط",
        "taxed_selling_price": "35"
    },
    {
        "id": "1182",
        "name": "واير تلفون خارجي أسود",
        "taxed_selling_price": "15"
    },
    {
        "id": "1187",
        "name": "واير تلفون داخلي",
        "taxed_selling_price": "12"
    },
    {
        "id": "1554",
        "name": "واير كمبيوتر 50m",
        "taxed_selling_price": "45"
    },
    {
        "id": "1837",
        "name": "ماسورة 1 1/2 إنش حار (بالمتر)",
        "taxed_selling_price": "17"
    },
    {
        "id": "1922",
        "name": "ماسورة 1/2 إنش بارد (بالمتر)",
        "taxed_selling_price": "3.5"
    },
    {
        "id": "1986",
        "name": "ماسورة 3/4 إنش بارد",
        "taxed_selling_price": "24"
    },
    {
        "id": "1987",
        "name": "ماسورة 1 إنش حار امريكي",
        "taxed_selling_price": "92"
    },
    {
        "id": "2013",
        "name": "ماسورة 1 إنش بارد",
        "taxed_selling_price": "24"
    },
    {
        "id": "2014",
        "name": "ماسورة 1 إنش حار بحريني BP",
        "taxed_selling_price": "39"
    },
    {
        "id": "2015",
        "name": "ماسورة كهرباء 3/4 اسود",
        "taxed_selling_price": "3"
    },
    {
        "id": "2045",
        "name": "واير سبيكر (بالمتر)",
        "taxed_selling_price": "1"
    },
    {
        "id": "2046",
        "name": "واير سينار (رولة كاملة) 1.5 mm",
        "taxed_selling_price": "90"
    },
    {
        "id": "2058",
        "name": "واير مزدوج (بالمتر)",
        "taxed_selling_price": "1"
    },
    {
        "id": "2063",
        "name": "ماسورة 2 إنش DPM (بالمتر)",
        "taxed_selling_price": "12"
    },
    {
        "id": "2091",
        "name": "واير 1.5*3 إنش (بالمتر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "2098",
        "name": "واير اسود مغلف",
        "taxed_selling_price": "2"
    },
    {
        "id": "2279",
        "name": "ماسورة 1 1/2 إنش (سم)",
        "taxed_selling_price": "0.06"
    },
    {
        "id": "2280",
        "name": "ماسورة 1 1/2 إنش بارد(سم)",
        "taxed_selling_price": "0.1"
    },
    {
        "id": "2281",
        "name": "ماسورة 1 إنش أبيض (بالمتر)",
        "taxed_selling_price": "4"
    },
    {
        "id": "2282",
        "name": "ماسورة 1 إنش بارد (سم)",
        "taxed_selling_price": "0.06"
    },
    {
        "id": "2283",
        "name": "ماسورة 1 1/2 إنش بحريني بلاستيكو",
        "taxed_selling_price": "27"
    },
    {
        "id": "2284",
        "name": "ماسورة 1 1/2 إنش حار بحريني",
        "taxed_selling_price": "80"
    },
    {
        "id": "2285",
        "name": "ماسورة 1 إنش بارد بحريني",
        "taxed_selling_price": "29"
    },
    {
        "id": "2286",
        "name": "ماسورة 1 إنش حار بحريني (سم)",
        "taxed_selling_price": "0.075"
    },
    {
        "id": "2287",
        "name": "ماسورة 1/2 إنش أبيض (بالمتر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "2288",
        "name": "ماسورة 1/2 إنش بارد (سم)",
        "taxed_selling_price": "0.025"
    },
    {
        "id": "2289",
        "name": "ماسورة 4 إنش ض 40",
        "taxed_selling_price": "100"
    },
    {
        "id": "2326",
        "name": "ماسورة 1/2 إنش حار بحريني (سم)",
        "taxed_selling_price": "0.05"
    },
    {
        "id": "2327",
        "name": "ماسورة 2 إنش MMP (سم)",
        "taxed_selling_price": "0.07"
    },
    {
        "id": "2328",
        "name": "ماسورة 2 إنش وسط",
        "taxed_selling_price": "52"
    },
    {
        "id": "2329",
        "name": "ماسورة 3 إنش MMP (سم)",
        "taxed_selling_price": "0.12"
    },
    {
        "id": "2330",
        "name": "ماسورة 3/4 إنش أبيض (سم)",
        "taxed_selling_price": "0.03"
    },
    {
        "id": "2331",
        "name": "ماسورة 3/4 إنش بارد (سم)",
        "taxed_selling_price": "0.05"
    },
    {
        "id": "2332",
        "name": "ماسورة 3/4 إنش حار بحريني (سم)",
        "taxed_selling_price": "0.06"
    },
    {
        "id": "2333",
        "name": "ماسورة 4 إنش MMP (سم)",
        "taxed_selling_price": "0.18"
    },
    {
        "id": "2335",
        "name": "ماسورة 6 انش DPM (بالمتر)",
        "taxed_selling_price": "45"
    },
    {
        "id": "2357",
        "name": "واير تلفون داخلي بالمتر",
        "taxed_selling_price": "1"
    },
    {
        "id": "2402",
        "name": "واير مسجل",
        "taxed_selling_price": "5"
    },
    {
        "id": "2403",
        "name": "واير دش 20يارده",
        "taxed_selling_price": "10"
    },
    {
        "id": "2404",
        "name": "واير دش 50يارده",
        "taxed_selling_price": "15"
    },
    {
        "id": "2477",
        "name": "واير كمبيوتر 3m",
        "taxed_selling_price": "8"
    },
    {
        "id": "2483",
        "name": "واير 2.5*3",
        "taxed_selling_price": "250"
    },
    {
        "id": "2485",
        "name": "واير 2.5*3 mm (بالمتر)",
        "taxed_selling_price": "4"
    },
    {
        "id": "2487",
        "name": "ماسورة 1 1/2 إنش بارد",
        "taxed_selling_price": "55"
    },
    {
        "id": "2493",
        "name": "واير 4*3 mm (بالمتر)",
        "taxed_selling_price": "6"
    },
    {
        "id": "2494",
        "name": "واير 4*2 إنش",
        "taxed_selling_price": "220"
    },
    {
        "id": "2519",
        "name": "ماسورة 6 انش DPM",
        "taxed_selling_price": "195"
    },
    {
        "id": "2520",
        "name": "ماسورة 4 انش DPM",
        "taxed_selling_price": "113"
    },
    {
        "id": "2521",
        "name": "ماسورة 3 انش DPM",
        "taxed_selling_price": "95"
    },
    {
        "id": "2522",
        "name": "ماسورة 2 انش DPM",
        "taxed_selling_price": "53"
    },
    {
        "id": "2523",
        "name": "ماسورة 2 إنش بارد بحريني",
        "taxed_selling_price": "75"
    },
    {
        "id": "2524",
        "name": "ماسورة 2 إنش بارد (بالمتر)",
        "taxed_selling_price": "12"
    },
    {
        "id": "2555",
        "name": "ماسورة 1/2 إنش حار بحريني",
        "taxed_selling_price": "25"
    },
    {
        "id": "2556",
        "name": "ماسورة 1/2 إنش بارد بحريني",
        "taxed_selling_price": "20"
    },
    {
        "id": "2629",
        "name": "ماسورة 3/4 إنش حار امريكي",
        "taxed_selling_price": "68"
    },
    {
        "id": "2630",
        "name": "ماسورة 6 انش بحريني تايلوس",
        "taxed_selling_price": "260"
    },
    {
        "id": "2631",
        "name": "ماسورة 4 انش 5 ملي",
        "taxed_selling_price": "135"
    },
    {
        "id": "2632",
        "name": "ماسورة 2 انش ض 40",
        "taxed_selling_price": "39"
    },
    {
        "id": "2645",
        "name": "ماسورة 6 انش (سم)",
        "taxed_selling_price": "0.33"
    },
    {
        "id": "2707",
        "name": "واير كمبيوتر 20m",
        "taxed_selling_price": "25"
    },
    {
        "id": "2708",
        "name": "واير كمبيوتر 30m",
        "taxed_selling_price": "35"
    },
    {
        "id": "2748",
        "name": "واير مزدوج 1.5 ملي",
        "taxed_selling_price": "35"
    },
    {
        "id": "2825",
        "name": "واير دش امريكي (بالمتر)",
        "taxed_selling_price": "1.5"
    },
    {
        "id": "2830",
        "name": "ماسورة 3 انش 4.7",
        "taxed_selling_price": "90"
    },
    {
        "id": "3012",
        "name": "واير الرياض مقاس 8 (بالمتر)",
        "taxed_selling_price": "5"
    },
    {
        "id": "3019",
        "name": "واير كمبيوتر 10m",
        "taxed_selling_price": "15"
    },
    {
        "id": "3020",
        "name": "واير كمبيوتر 2m",
        "taxed_selling_price": "5"
    },
    {
        "id": "3120",
        "name": "واير مقاس 14",
        "taxed_selling_price": "165"
    },
    {
        "id": "3121",
        "name": "واير الرياض مقاس 12",
        "taxed_selling_price": "260"
    },
    {
        "id": "3122",
        "name": "واير الرياض مقاس 10",
        "taxed_selling_price": "390"
    },
    {
        "id": "3348",
        "name": "ماسورة 4 إنش (كهرباء)",
        "taxed_selling_price": "45"
    },
    {
        "id": "3427",
        "name": "واير 4*2 mm (بالمتر)",
        "taxed_selling_price": "5"
    },
    {
        "id": "3436",
        "name": "واير بروش صغير",
        "taxed_selling_price": "4"
    },
    {
        "id": "3542",
        "name": "واير تلفون 3 باير امريكي",
        "taxed_selling_price": "125"
    },
    {
        "id": "3548",
        "name": "واير دش امريكي 300 FT",
        "taxed_selling_price": "85"
    },
    {
        "id": "3594",
        "name": "واير تلفون 6 باير امريكي",
        "taxed_selling_price": "255"
    },
    {
        "id": "3603",
        "name": "واير تلفون 2 باير امريكي (بالمتر)",
        "taxed_selling_price": "1"
    },
    {
        "id": "3657",
        "name": "واير تلفون 4 باير امريكي",
        "taxed_selling_price": "215"
    },
    {
        "id": "3659",
        "name": "واير تلفون 2 باير امريكي",
        "taxed_selling_price": "120"
    },
    {
        "id": "3669",
        "name": "واير سينار 1/2 mm",
        "taxed_selling_price": "0.5"
    },
    {
        "id": "3686",
        "name": "واير تلفون 6 باير امريكي (بالمتر)",
        "taxed_selling_price": "2.5"
    },
    {
        "id": "3695",
        "name": "واير تلفون 8 باير امريكي (بالمتر)",
        "taxed_selling_price": "3"
    },
    {
        "id": "3714",
        "name": "واير مزدوج",
        "taxed_selling_price": "45"
    },
    {
        "id": "3722",
        "name": "ماسورة 4 إنش بحريني تايلوس",
        "taxed_selling_price": "165"
    },
    {
        "id": "3737",
        "name": "واير DSL إسترالي",
        "taxed_selling_price": "150"
    },
    {
        "id": "3738",
        "name": "ماسورة 3 انش خفيف (بالمتر)",
        "taxed_selling_price": "7"
    },
    {
        "id": "3741",
        "name": "ماسورة 2 خفيف بالمتر",
        "taxed_selling_price": "4"
    },
    {
        "id": "3755",
        "name": "ماسورة 3 انش ض 40",
        "taxed_selling_price": "70"
    },
    {
        "id": "3756",
        "name": "واير سينار 2 ملي 80 ياردة",
        "taxed_selling_price": "80"
    },
    {
        "id": "3769",
        "name": "واير تلفون 4 باير (بالمتر)",
        "taxed_selling_price": "2.5"
    },
    {
        "id": "3777",
        "name": "واير DSL جنرال",
        "taxed_selling_price": "450"
    },
    {
        "id": "3780",
        "name": "كيبل 50 ملي الرياض",
        "taxed_selling_price": "43"
    },
    {
        "id": "3781",
        "name": "سلك نحاس 25 ملي",
        "taxed_selling_price": "7.5"
    },
    {
        "id": "3782",
        "name": "سلك نحاس 35 ملي",
        "taxed_selling_price": "10"
    },
    {
        "id": "3785",
        "name": "كيبل 50 ملي الرياض 4 خط",
        "taxed_selling_price": "55"
    },
    {
        "id": "3786",
        "name": "كيبل 35 ملي الرياض 4 خط",
        "taxed_selling_price": "39"
    },
    {
        "id": "3787",
        "name": "سيخ كران +كنكتر",
        "taxed_selling_price": "22"
    },
    {
        "id": "3792",
        "name": "واير دش امريكي 1000 FT",
        "taxed_selling_price": "285"
    },
    {
        "id": "3793",
        "name": "واير تلفون 3 بير امريكي (بالمتر)",
        "taxed_selling_price": "2"
    },
    {
        "id": "3806",
        "name": "واير تلفون 8 باير امريكي",
        "taxed_selling_price": "330"
    },
    {
        "id": "3810",
        "name": "واير بروش طقم كبير",
        "taxed_selling_price": "12"
    },
    {
        "id": "3812",
        "name": "واير مسك مقاس 8",
        "taxed_selling_price": "590"
    },
    {
        "id": "3821",
        "name": "ماسورة 3 إنش بحريني كهرباء 2.16",
        "taxed_selling_price": "40"
    },
    {
        "id": "3823",
        "name": "واير سبيكر 80 ياردة",
        "taxed_selling_price": "30"
    },
    {
        "id": "3831",
        "name": "واير مقاس 14",
        "taxed_selling_price": "80"
    },
    {
        "id": "3833",
        "name": "ماسورة 2 إنش حار (بالمتر)",
        "taxed_selling_price": "22"
    },
    {
        "id": "3847",
        "name": "ماسورة 3 انش (DPM) بالمتر",
        "taxed_selling_price": "20"
    },
    {
        "id": "3848",
        "name": "واير سينار 1/2 mm (رولة)",
        "taxed_selling_price": "35"
    },
    {
        "id": "3857",
        "name": "ماسورة 4 انش DPM (بالمتر)",
        "taxed_selling_price": "25"
    },
    {
        "id": "3862",
        "name": "كيبل 35 ملي إلترا 4 خط",
        "taxed_selling_price": "32"
    },
    {
        "id": "3866",
        "name": "واير تلفون اسود 450 ياردة",
        "taxed_selling_price": "60"
    },
    {
        "id": "3870",
        "name": "كيبل 70 ملي الرياض 4 خط",
        "taxed_selling_price": "83"
    },
    {
        "id": "3874",
        "name": "سلك نحاس 16 ملي",
        "taxed_selling_price": "5.5"
    },
    {
        "id": "3882",
        "name": "واير كمبيوتر m 5",
        "taxed_selling_price": "10"
    },
    {
        "id": "3883",
        "name": "واير كمبيوتر m 15",
        "taxed_selling_price": "20"
    },
    {
        "id": "3916",
        "name": "واير الترا مقاس 10",
        "taxed_selling_price": "330"
    },
    {
        "id": "3918",
        "name": "واير الترا مفاس 14",
        "taxed_selling_price": "155"
    },
    {
        "id": "3926",
        "name": "ماسورة 6 انش سبيشل",
        "taxed_selling_price": "180"
    },
    {
        "id": "3948",
        "name": "كيبل 25 ملي",
        "taxed_selling_price": "25.5"
    },
    {
        "id": "3973",
        "name": "واير دش 1000 FT",
        "taxed_selling_price": "210"
    },
    {
        "id": "3974",
        "name": "كيبل 25 ملي الرياض 4 خط",
        "taxed_selling_price": "50"
    },
    {
        "id": "4036",
        "name": "ماسورة 10 إنش بلاسكو",
        "taxed_selling_price": "235"
    },
    {
        "id": "4054",
        "name": "ماسورة 8 انش بلاسكو",
        "taxed_selling_price": "325"
    },
    {
        "id": "4069",
        "name": "واير سبيكر 75*2 ملي",
        "taxed_selling_price": "40"
    },
    {
        "id": "4082",
        "name": "واير تلفون 12 باير امريكي",
        "taxed_selling_price": "500"
    },
    {
        "id": "4104",
        "name": "كيبل 185*4 (الرياض)",
        "taxed_selling_price": "250"
    },
    {
        "id": "4117",
        "name": "كيبل 16 ملي الرياض 4 خط",
        "taxed_selling_price": "22"
    },
    {
        "id": "4122",
        "name": "واير 2*1/2 1 ملي",
        "taxed_selling_price": "115"
    },
    {
        "id": "4135",
        "name": "كيبل 95 ملي الرياض 4 خط",
        "taxed_selling_price": "97"
    },
    {
        "id": "4139",
        "name": "ماسورة 11/4 إنش حار",
        "taxed_selling_price": "85"
    },
    {
        "id": "4141",
        "name": "واير 3*6 ملي",
        "taxed_selling_price": "500"
    },
    {
        "id": "4142",
        "name": "واير كمبيوتر 40 m",
        "taxed_selling_price": "45"
    },
    {
        "id": "4199",
        "name": "كيبل 185*4 الرياض",
        "taxed_selling_price": "165"
    },
    {
        "id": "4204",
        "name": "وايلر كمبيوتر 25 m",
        "taxed_selling_price": "25"
    },
    {
        "id": "4207",
        "name": "كيبل 150*4 الرياض",
        "taxed_selling_price": "164"
    },
    {
        "id": "4209",
        "name": "ماسورة 3 سبيشل",
        "taxed_selling_price": "49"
    },
    {
        "id": "4219",
        "name": "ماسورة 6 انش دي بي ام 5 ملي",
        "taxed_selling_price": "220"
    },
    {
        "id": "4230",
        "name": "واير كران 50 ملي",
        "taxed_selling_price": "15"
    },
    {
        "id": "4242",
        "name": "كيبل 35 ملي الترا",
        "taxed_selling_price": "26"
    },
    {
        "id": "4243",
        "name": "كيبل 50 ملي الترا",
        "taxed_selling_price": "38"
    },
    {
        "id": "4244",
        "name": "كيبل 95 ملي الترا",
        "taxed_selling_price": "77"
    },
    {
        "id": "4248",
        "name": "ماسورة 2 انش بحريني تايلوس",
        "taxed_selling_price": "63"
    },
    {
        "id": "4254",
        "name": "ماسورة 6 انش 5 ملي",
        "taxed_selling_price": "165"
    },
    {
        "id": "4272",
        "name": "كيبل 16 ملي الرياض",
        "taxed_selling_price": "18"
    },
    {
        "id": "4295",
        "name": "ماسورة 2 انش بارد",
        "taxed_selling_price": "65"
    },
    {
        "id": "4299",
        "name": "كيبل 120*4 الرياض",
        "taxed_selling_price": "119"
    },
    {
        "id": "4329",
        "name": "واير مقاس 6 ملي",
        "taxed_selling_price": "920"
    },
    {
        "id": "4348",
        "name": "واير 3*1.5",
        "taxed_selling_price": "170"
    },
    {
        "id": "4349",
        "name": "واير 3*4",
        "taxed_selling_price": "325"
    }
]


function extract_unit(item_name) {
    var words = item_name.split(" ");
    //check if it has a unit in the name like بالمتر, متر، سم
    for(var word of words) {
        if(word.includes('متر') || word.includes('سم') || word.includes('مللي') || word.includes('كيلو')){
            return word.trim();
        }
    }
}

function find_parent(item_name) {
    var unit = extract_unit(item_name);
    var name_without_unit = item_name.replace(unit, "").trim().replace(/\s+/g, ' ');
    for(var item of items) {
        if(item.name.trim().replace(/\s+/g, ' ') == name_without_unit) {
            return item;
        }
    }
}

var sub_items_with_parent = [];
var sub_items_without_parent = [];
var ignore = [];

function get_conv_fact(item_name) {

    if(item_name.includes('متر') && item_name.includes('ماسورة')) {
        return 1/6;
    } 
    if(item_name.includes('سم') && item_name.includes('ماسورة')) {
        return 1/600;
    } else {
        return 1/150
    }
}

var a = -1;
for(var item of items) {
    a++;
    var unit = extract_unit(item.name);
    if(unit) {
        var parent = find_parent(item.name);
        if(parent) {
            new_item = {
                "parent_id": parent.id,
                "parent_name": parent.name.replace(unit, "").trim().replace(/\s+/g, ' '),
                "parent_unit" : 'PCE',
                "item_id": item.id,
                "name": item.name,
                "unit": unit.includes('سم') ? 'CM': 'MTR',
                "taxed_price": item.taxed_selling_price,
                "conversion_factor" : get_conv_fact(item.name),
            }
            sub_items_with_parent.push(new_item);
        } else {
            new_item = {
                "parent_id": item.id,
                "parent_name": item.name.replace(unit, "").trim().replace(/\s+/g, ' '),
                "parent_unit" : 'PCE',
                "item_id": item.id,
                "name": item.name,
                "unit": unit.includes('سم') ? 'CM': 'MTR',
                "taxed_price": item.taxed_selling_price,
                "conversion_factor" : get_conv_fact(item.name),
            }
            sub_items_without_parent.push(new_item);
        }
    } else {
        ignore.push(item);
    }
}

var obj = {
    "sub_items_with_parent": sub_items_with_parent,
    "sub_items_without_parent": sub_items_without_parent,
    //"ignore": ignore
};

const fs = require('fs');
const text = stringifyWithArraysOnSingleLines(obj);
const filePath = 'migrate_units.json';

fs.writeFileSync(filePath, text, 'utf8');


// Custom stringify that adds a newline between array entries
function stringifyWithArraysOnSingleLines(obj) {
    const entries = Object.entries(obj).map(([key, value]) => {
      if (Array.isArray(value)) {
        const lines = value.map(v => '    ' + JSON.stringify(v)).join(',\n');
        return `  "${key}": [\n${lines}\n  ]`;
      } else {
        return `  "${key}": ${JSON.stringify(value)}`;
      }
    });
    return `{\n${entries.join(',\n')}\n}`;
  }