/**
 * Database migrator for SQLite3
 *
 * This script handles the generic parts of the migration:
 * - Database connections
 * - Performance optimizations
 * - Attaching/detaching databases
 * - Transaction management
 * - Cleanup
 */

const sqlite3 = require('sqlite3').verbose();
const { performance } = require('perf_hooks');

// Paths to the databases
const OLD_DB_PATH = 'D:/old/github/n912/spa/pos3/db/old_db.sqlite3';
const NEW_DB_PATH = 'D:/old/github/n912/spa/pos3/db/db.sqlite3';

// Configuration flags
var migrate_customers = true;
var migrate_suppliers = true;
var migrate_items = true;
var migrate_sales = true;
var migrate_purchase_orders = true; // Migrate purchase orders
var migrate_units = true; // Fix units after sales orders
var migrate_arabic_transactions = true; // Create all transactions using Arabic chart of accounts
var migrate_arabic_inventory = true; // Create all inventory transactions using Arabic descriptions
var migrate_inventory_returns = true; // Migrate standalone inventory returns from old database
var fix_missing_item_costs = true; // Fix items with missing inventory transactions
var update_sales_calculations = true; // Update sales calculations after all transactions
var update_purchase_calculations = true; // Update purchase calculations after all transactions

// Calculation method configuration
var use_new_calculation_method = true; // true = new unit-based formula, false = previous logic

// Import the migration modules
const customerMigration = require('./migrate-customers');
const supplierMigration = require('./migrate-suppliers');
const itemMigration = require('./migrate-items');
const salesOrderMigration = require('./migrate-sales-orders-optimized'); // Using the optimized version
const purchaseOrderMigration = require('./migrate-purchase-orders'); // Purchase orders migration
const unitsMigration = require('./migrate-units'); // Units fix after sales orders
const arabicTransactionsMigration = require('./migrate-arabic-transactions'); // Create all transactions using Arabic chart of accounts
const arabicInventoryTransactionsMigration = require('./migrate-arabic-inventory-transactions'); // Create all inventory transactions using Arabic descriptions
const inventoryReturnsMigration = require('./migrate-inventory-returns'); // Migrate standalone inventory returns from old database
const fixMissingItemCostsMigration = require('./fix-missing-item-costs'); // Fix items with missing inventory transactions
const updateSalesCalculationsMigration = require('./update-sales-calculations'); // Update sales calculations after all transactions
const updatePurchaseCalculationsMigration = require('./update-purchase-calculations'); // Update purchase calculations after all transactions

// Start timing
const startTime = performance.now();

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

// Open database with promise
function openDatabase(path, mode) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(path, mode, err => {
      if (err) reject(err);
      else resolve(db);
    });
  });
}

// Close database with promise
function closeDatabase(db) {
  return new Promise((resolve, reject) => {
    db.close(err => {
      if (err) reject(err);
      else resolve();
    });
  });
}

// Main function
async function startMigration() {
  let db;

  try {
    console.log('Starting migration...');

    // Open the new database
    db = await openDatabase(NEW_DB_PATH, sqlite3.OPEN_READWRITE);
    console.log('Connected to new database.');

    // Apply performance optimizations
    await run(db, 'PRAGMA synchronous = OFF');
    await run(db, 'PRAGMA journal_mode = MEMORY');
    await run(db, 'PRAGMA temp_store = MEMORY');
    await run(db, 'PRAGMA locking_mode = EXCLUSIVE');
    await run(db, 'PRAGMA cache_size = 100000');
    await run(db, 'PRAGMA foreign_keys = OFF');

    // Attach the old database
    console.log('Attaching old database...');
    await run(db, `ATTACH DATABASE '${OLD_DB_PATH}' AS old_db`);
    console.log('Old database attached successfully.');

    // Begin transaction
    await run(db, 'BEGIN TRANSACTION');

    // Check if any migration is selected
    if (!migrate_customers && !migrate_suppliers && !migrate_items && !migrate_sales && !migrate_purchase_orders && !migrate_units &&
        !migrate_arabic_transactions && !migrate_arabic_inventory && !migrate_inventory_returns && !fix_missing_item_costs &&
        !update_sales_calculations && !update_purchase_calculations) {
      console.log('No migration selected. Set at least one migration flag to true.');
      await run(db, 'ROLLBACK');
      await cleanup(db);
      return;
    }

    // Define the migrations to run
    const migrations = [];

    if (migrate_customers) {
      migrations.push({
        name: 'customer',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            customerMigration.migrateCustomers(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_suppliers) {
      migrations.push({
        name: 'supplier',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            supplierMigration.migrateSuppliers(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_items) {
      migrations.push({
        name: 'item',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            itemMigration.migrateItems(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_sales) {
      migrations.push({
        name: 'sales order',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            salesOrderMigration.migrateSalesOrders(db, startTime, use_new_calculation_method, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_purchase_orders) {
      migrations.push({
        name: 'purchase orders',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            purchaseOrderMigration.migratePurchaseOrders(db, startTime, use_new_calculation_method, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_units) {
      migrations.push({
        name: 'units fix',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            unitsMigration.migrateUnits(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_arabic_transactions) {
      migrations.push({
        name: 'Arabic transactions',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            arabicTransactionsMigration.migrateArabicTransactions(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_arabic_inventory) {
      migrations.push({
        name: 'Arabic inventory transactions',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            arabicInventoryTransactionsMigration.migrateArabicInventoryTransactions(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (migrate_inventory_returns) {
      migrations.push({
        name: 'inventory returns',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            inventoryReturnsMigration.migrateInventoryReturnsMain(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (update_sales_calculations) {
      migrations.push({
        name: 'sales calculations update',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            updateSalesCalculationsMigration.updateSalesOrderCalculations(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (update_purchase_calculations) {
      migrations.push({
        name: 'purchase calculations update',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            updatePurchaseCalculationsMigration.updatePurchaseOrderCalculations(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    if (fix_missing_item_costs) {
      migrations.push({
        name: 'missing item costs (final step)',
        func: (db, startTime) => {
          return new Promise((resolve, reject) => {
            fixMissingItemCostsMigration.fixMissingItemCostsMain(db, startTime, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
        }
      });
    }

    // Run migrations sequentially
    for (let i = 0; i < migrations.length; i++) {
      const migration = migrations[i];
      console.log(`Running ${migration.name} migration...`);

      await migration.func(db, startTime);

      console.log(`${migration.name} migration completed successfully.`);
    }

    // Commit the transaction
    await run(db, 'COMMIT');
    console.log('All migrations completed successfully.');
  } catch (err) {
    console.error(`Error during migration: ${err.message}`);
    if (db) {
      try {
        await run(db, 'ROLLBACK');
      } catch (rollbackErr) {
        console.error(`Error rolling back transaction: ${rollbackErr.message}`);
      }
    }
  } finally {
    if (db) {
      await cleanup(db);
    }
  }
}

// Cleanup function
async function cleanup(db) {
  console.log('Cleaning up...');

  try {
    // Detach the old database
    await run(db, 'DETACH DATABASE old_db');
    console.log('Old database detached.');
  } catch (err) {
    console.error(`Error detaching old database: ${err.message}`);
  }

  try {
    // Close the database connection
    await closeDatabase(db);
    console.log('Database connection closed.');
  } catch (err) {
    console.error(`Error closing database: ${err.message}`);
  }
}

// Run the migration
startMigration().catch(err => {
  console.error(`Unhandled error: ${err.message}`);
  process.exit(1);
});
