/**
 * Update Sales Calculations module
 *
 * This module updates the PaidAmount and RemainingAmount calculations for sales orders
 * after all transactions have been created.
 */

const { performance } = require('perf_hooks');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

/**
 * Update PaidAmount and RemainingAmount calculations for sales orders
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function updateSalesCalculations(db, batchSize = 10000) {
  console.log('Updating PaidAmount and RemainingAmount calculations for sales orders...');

  // Get total count of orders
  const ordersCount = await get(db, 'SELECT COUNT(*) as count FROM SalesOrders');
  console.log(`Found ${ordersCount.count} orders to update.`);

  // Process in batches
  const orderBatchSize = Math.min(batchSize, ordersCount.count);
  const orderBatches = Math.ceil(ordersCount.count / orderBatchSize);

  for (let batch = 0; batch < orderBatches; batch++) {
    const offset = batch * orderBatchSize;
    console.log(`Processing orders batch ${batch + 1}/${orderBatches} (offset: ${offset})...`);

    await run(db, `
      UPDATE SalesOrders
      SET
        RemainingAmount = CASE
          WHEN CustomerID = 'C-UNKNOWN' THEN
            -- For unknown customers: RemainingAmount = 0
            0
          ELSE
            -- For known customers: RemainingAmount = Sum(Debit of customer) - Sum(Credit of customer)
            -- Only consider transactions related to this specific order
            (SELECT COALESCE(SUM(Amount), 0) FROM Transactions
             WHERE DebitAccountID = SalesOrders.CustomerID AND ReferenceID = SalesOrders.OrderID) -
            (SELECT COALESCE(SUM(Amount), 0) FROM Transactions
             WHERE CreditAccountID = SalesOrders.CustomerID AND ReferenceID = SalesOrders.OrderID)
          END,
        PaidAmount = CASE
          WHEN CustomerID = 'C-UNKNOWN' THEN
            -- For unknown customers: PaidAmount = TotalAfterReturns
            TotalAfterReturns
          ELSE
            -- For known customers: PaidAmount = TotalAfterReturns - RemainingAmount
            TotalAfterReturns - (
              (SELECT COALESCE(SUM(Amount), 0) FROM Transactions
               WHERE DebitAccountID = SalesOrders.CustomerID AND ReferenceID = SalesOrders.OrderID) -
              (SELECT COALESCE(SUM(Amount), 0) FROM Transactions
               WHERE CreditAccountID = SalesOrders.CustomerID AND ReferenceID = SalesOrders.OrderID)
            )
          END
      WHERE rowid IN (
        SELECT rowid FROM SalesOrders
        ORDER BY rowid
        LIMIT ${orderBatchSize} OFFSET ${offset}
      )
    `);
  }

  console.log('PaidAmount and RemainingAmount calculations updated successfully.');
}

/**
 * Main function to update sales calculations
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function updateSalesOrderCalculations(db, startTime, callback) {
  try {
    console.log('Starting sales order calculations update...');

    // Update PaidAmount and RemainingAmount calculations
    await updateSalesCalculations(db);

    const endTime = performance.now();
    console.log(`Sales order calculations update completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error updating sales order calculations: ${err.message}`);
    callback(err);
  }
}

module.exports = { updateSalesOrderCalculations };
