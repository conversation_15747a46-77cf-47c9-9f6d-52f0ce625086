/**
 * Direct Payments to Transactions Migration Module
 *
 * This module migrates payments directly from the old database's sales_order_payments table
 * to the new database's Transactions table, bypassing the Payments table entirely.
 * It handles the special case where customer is null by creating a single transaction
 * (debit cash, credit sales).
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Create transactions for payments directly from old database
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function createPaymentTransactions(db, batchSize = 10000) {
  console.log('Creating transactions for payments directly from old database...');

  // First, handle the case for unknown customers (null customer_id)
  // For these, we'll create a single transaction per sales order: debit cash, credit sales
  console.log('Creating transactions for sales with unknown customers...');

  // Get all sales orders with null customer_id
  const unknownCustomerOrders = await all(db, `
    SELECT
      om.new_id as new_order_id,
      o.transaction_date as order_date,
      s.InvoiceNumber,
      s.InvoiceTotalAmount as amount
    FROM old_db.sales_orders o
    JOIN order_mapping om ON o.id = om.old_id
    JOIN SalesOrders s ON om.new_id = s.OrderID
    WHERE o.confirmed = 1
    AND o.customer_id IS NULL
    AND NOT EXISTS (
      SELECT 1 FROM old_db.sales_order_changes c
      WHERE c.sales_order_id = o.id
    )
  `);

  console.log(`Found ${unknownCustomerOrders.length} sales orders with unknown customers and no returns.`);

  // Create transactions for unknown customer orders
  if (unknownCustomerOrders.length > 0) {
    let transactionValues = [];
    const cashAccountId = '1100'; // Cash account ID
    const salesAccountId = '4100'; // Sales account ID
    const now = new Date().toISOString();

    for (const order of unknownCustomerOrders) {
      const transactionId = uuidv4();
      // Create a single transaction: Debit cash, credit sales
      transactionValues.push(`(
        '${transactionId}',
        '${order.order_date}',
        'Cash payment for invoice ${order.InvoiceNumber}',
        '${cashAccountId}',
        '${salesAccountId}',
        ${order.amount},
        '${order.new_order_id}',
        'Payment',
        'migration',
        '${now}'
      )`);
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    console.log(`Created ${unknownCustomerOrders.length} transactions for sales with unknown customers.`);
  }

  // For unknown customers, we don't create any refund or payment transactions
  // They only have a single sales transaction created in migrate-transactions.js
  console.log('Skipping returns for unknown customers as per requirements.');

  // Now handle payments for known customers
  console.log('Creating transactions for payments with known customers...');

  // Get total count of payments to migrate for known customers
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.sales_order_payments p
    JOIN old_db.sales_orders o ON p.sales_order_id = o.id
    WHERE o.confirmed = 1
    AND o.customer_id IS NOT NULL
  `);
  const totalPayments = countResult.count;
  console.log(`Found ${totalPayments} payments with known customers to process.`);

  // Process in batches
  const batches = Math.ceil(totalPayments / batchSize);
  console.log(`Will process in ${batches} batches of ${batchSize} payments each.`);

  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of payments with sales order information for known customers
    const payments = await all(db, `
      SELECT
        p.id as payment_id,
        p.sales_order_id as old_order_id,
        p.amount,
        p.payment_date,
        o.customer_id as old_customer_id,
        o.transaction_date as order_date,
        om.new_id as new_order_id,
        s.InvoiceNumber
      FROM old_db.sales_order_payments p
      JOIN old_db.sales_orders o ON p.sales_order_id = o.id
      JOIN order_mapping om ON p.sales_order_id = om.old_id
      JOIN SalesOrders s ON om.new_id = s.OrderID
      WHERE o.confirmed = 1
      AND o.customer_id IS NOT NULL
      ORDER BY p.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (payments.length === 0) {
      console.log(`No payments found in batch ${batch + 1}.`);
      continue;
    }

    console.log(`Processing ${payments.length} payments in batch ${batch + 1}...`);

    // Prepare batch insert statement
    let transactionValues = [];

    for (const payment of payments) {
      // Create a transaction for the payment
      const transactionId = uuidv4();
      const amount = Math.abs(payment.amount); // Use absolute value to handle negative amounts
      const cashAccountId = '1100'; // Cash account ID
      const now = new Date().toISOString();

      // Get customer ID from the new database
      const customer = await get(db, `
        SELECT CustomerID
        FROM SalesOrders
        WHERE OrderID = ?
      `, [payment.new_order_id]);

      const customerID = customer ? customer.CustomerID : 'C-UNKNOWN';

      if (payment.amount >= 0) {
        // Positive amount: Customer is paying
        // Debit: Cash/Bank Account (Asset increases)
        // Credit: Customer Account (Asset decreases)
        transactionValues.push(`(
          '${transactionId}',
          '${payment.payment_date}',
          'Customer payment for invoice ${payment.InvoiceNumber}',
          '${cashAccountId}',
          '${customerID}',
          ${amount},
          '${payment.new_order_id}',
          'CustomerPayment',
          'migration',
          '${now}'
        )`);
      } else {
        // Negative amount: Refund to customer
        // Debit: Customer Account (Asset increases)
        // Credit: Cash/Bank Account (Asset decreases)
        transactionValues.push(`(
          '${transactionId}',
          '${payment.payment_date}',
          'Customer refund for invoice ${payment.InvoiceNumber}',
          '${customerID}',
          '${cashAccountId}',
          ${amount},
          '${payment.new_order_id}',
          'CustomerRefund',
          'migration',
          '${now}'
        )`);
      }
    }

    // Insert transactions
    if (transactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${transactionValues.join(',')}
      `);
    }

    totalProcessed += payments.length;
    console.log(`Processed ${totalProcessed}/${totalPayments} payments (${Math.round(totalProcessed/totalPayments*100)}%).`);
  }

  console.log(`Created transactions for ${totalProcessed} payments with known customers.`);
}

/**
 * Create and populate order mapping table if needed
 * @param {object} db - Database connection
 */
async function createOrderMappingTable(db) {
  console.log('Creating order mapping table if needed...');

  // Create temporary mapping table if it doesn't exist
  await run(db, `
    CREATE TEMPORARY TABLE IF NOT EXISTS order_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Create index if it doesn't exist
  await run(db, `CREATE INDEX IF NOT EXISTS idx_order_mapping_new_id ON order_mapping(new_id)`);

  // Check if the table is empty
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM order_mapping');

  if (countResult.count === 0) {
    console.log('Populating order mapping table...');
    await run(db, `
      INSERT INTO order_mapping (old_id, new_id)
      SELECT o.id, s.OrderID
      FROM old_db.sales_orders o
      JOIN SalesOrders s ON 'SO-' || printf('%06d', o.id) = s.SalesOrderNumber
      WHERE o.confirmed = 1
    `);
    console.log('Order mapping table populated successfully.');
  } else {
    console.log(`Order mapping table already has ${countResult.count} entries.`);
  }
}

/**
 * Main function to migrate payments directly to transactions
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migratePaymentsToTransactions(db, startTime, callback) {
  try {
    console.log('Starting direct payments to transactions migration...');

    // Create and populate order mapping table if needed
    await createOrderMappingTable(db);

    // Clear existing payment transactions
    console.log('Clearing existing payment transactions...');
    await run(db, "DELETE FROM Transactions WHERE ReferenceType IN ('Payment', 'Refund', 'CustomerPayment', 'CustomerRefund')");

    // Create transactions for payments
    await createPaymentTransactions(db);

    const endTime = performance.now();
    console.log(`Direct payments to transactions migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating payments to transactions: ${err.message}`);
    callback(err);
  }
}

module.exports = { migratePaymentsToTransactions };
