/**
 * Migrate Inventory Returns from Old Database
 *
 * This module migrates standalone inventory returns (not sales order returns)
 * and creates both inventory transactions and financial transactions
 */

const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');

// Promisify SQLite functions
function run(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

function get(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function all(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

/**
 * Migrate inventory return details to inventory transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function migrateInventoryReturnDetails(db, batchSize = 1000) {
  console.log('Migrating inventory return details to inventory transactions...');

  // Get total count of confirmed inventory return details
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.inventory_returns ir
    JOIN old_db.inventory_return_details ird ON ir.id = ird.inventory_return_id
    WHERE ir.confirmed = 1
  `);
  const totalDetails = countResult.count;
  console.log(`Found ${totalDetails} confirmed inventory return details to process.`);

  if (totalDetails === 0) {
    console.log('No inventory return details to process.');
    return;
  }

  // Process in batches
  const batches = Math.ceil(totalDetails / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of inventory return details
    const details = await all(db, `
      SELECT
        ir.id as return_id,
        ird.id as detail_id,
        ird.item_id as old_item_id,
        ird.quantity,
        ird.unit_purchase_price as cost_price,
        ir.transaction_date as return_date,
        ir.note,
        i.ItemID as new_item_id,
        i.Name as item_name
      FROM old_db.inventory_returns ir
      JOIN old_db.inventory_return_details ird ON ir.id = ird.inventory_return_id
      JOIN Items i ON ird.item_id = i.ItemNumber
      WHERE ir.confirmed = 1
      ORDER BY ir.id, ird.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (details.length === 0) continue;

    let inventoryTransactionValues = [];

    for (const detail of details) {
      const inventoryTransactionId = uuidv4();

      // Create inventory transaction (positive quantity for returns)
      inventoryTransactionValues.push(`(
        '${inventoryTransactionId}',
        '${detail.new_item_id}',
        'INVENTORY_RETURN',
        ${detail.quantity},
        ${detail.cost_price || 0},
        '${detail.return_date}',
        'ارجاع للمخزن ${detail.return_id} - عودة ${detail.quantity} من ${detail.item_name}',
        'migration'
      )`);
    }

    // Insert inventory transactions
    if (inventoryTransactionValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${inventoryTransactionValues.join(',')}
      `);
    }

    totalProcessed += details.length;
    console.log(`Processed ${totalProcessed}/${totalDetails} details (${Math.round(totalProcessed/totalDetails*100)}%).`);
  }

  console.log(`Created inventory transactions for ${totalProcessed} inventory return details.`);
}

/**
 * Create customer mapping table for inventory returns
 * @param {object} db - Database connection
 */
async function createInventoryReturnCustomerMapping(db) {
  console.log('Creating customer mapping for inventory returns...');

  // Create temporary customer mapping table if it doesn't exist
  await run(db, `
    CREATE TEMPORARY TABLE IF NOT EXISTS inventory_return_customer_mapping (
      old_id INTEGER PRIMARY KEY,
      new_id TEXT NOT NULL
    )
  `);

  // Check if the table is empty
  const countResult = await get(db, 'SELECT COUNT(*) as count FROM inventory_return_customer_mapping');

  if (countResult.count === 0) {
    console.log('Populating inventory return customer mapping table...');

    // Get all unique customer IDs from inventory returns
    const customerIds = await all(db, `
      SELECT DISTINCT customer_id
      FROM old_db.inventory_returns
      WHERE confirmed = 1 AND customer_id IS NOT NULL
    `);

    console.log(`Found ${customerIds.length} unique customers in inventory returns.`);

    // For each customer in the old database, find the corresponding customer in the new database
    for (const row of customerIds) {
      const customerId = row.customer_id;

      // Get customer from old database
      const oldCustomer = await get(db, `
        SELECT id, full_name, tax_number
        FROM old_db.customers
        WHERE id = ?
      `, [customerId]);

      if (oldCustomer) {
        // Look for matching customer in new database by name
        const newCustomer = await get(db, `
          SELECT CustomerID
          FROM Customers
          WHERE Name = ?
        `, [oldCustomer.full_name]);

        if (newCustomer) {
          // Insert into mapping table
          await run(db, `
            INSERT OR IGNORE INTO inventory_return_customer_mapping (old_id, new_id)
            VALUES (?, ?)
          `, [oldCustomer.id, newCustomer.CustomerID]);
        }
      }
    }
    console.log('Inventory return customer mapping table populated.');
  } else {
    console.log(`Inventory return customer mapping table already has ${countResult.count} entries.`);
  }
}

/**
 * Migrate inventory returns to financial transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function migrateInventoryReturnHeaders(db, batchSize = 1000) {
  console.log('Migrating inventory return headers to financial transactions...');

  // Create customer mapping first
  await createInventoryReturnCustomerMapping(db);

  // Get total count of confirmed inventory returns
  const countResult = await get(db, `
    SELECT COUNT(*) as count
    FROM old_db.inventory_returns
    WHERE confirmed = 1
  `);
  const totalReturns = countResult.count;
  console.log(`Found ${totalReturns} confirmed inventory returns to process.`);

  if (totalReturns === 0) {
    console.log('No inventory returns to process.');
    return;
  }

  // Process in batches
  const batches = Math.ceil(totalReturns / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get batch of inventory returns with proper customer mapping
    const returns = await all(db, `
      SELECT
        ir.id as return_id,
        ir.customer_id as old_customer_id,
        ir.refunded,
        ir.transaction_date as return_date,
        ir.note,
        ir.returned_taxed_discounted_selling_price as total_amount,
        cm.new_id as new_customer_id
      FROM old_db.inventory_returns ir
      LEFT JOIN inventory_return_customer_mapping cm ON ir.customer_id = cm.old_id
      WHERE ir.confirmed = 1
      ORDER BY ir.id
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (returns.length === 0) continue;

    let financialTransactionValues = [];

    for (const returnItem of returns) {
      const financialTransactionId = uuidv4();
      const totalAmount = returnItem.total_amount || 0;

      if (totalAmount > 0) {
        if (!returnItem.old_customer_id) {
          // Cash return (no customer): Debit customer refund account, Credit cash
          financialTransactionValues.push(`(
            '${financialTransactionId}',
            '${returnItem.return_date}',
            'ارجاع للمخزن ${returnItem.return_id}',
            '4210',
            '1111',
            ${totalAmount},
            '${returnItem.return_id}',
            'INVENTORY_RETURN',
            'migration',
            '${new Date().toISOString()}'
          )`);
        } else if (returnItem.refunded === 1) {
          // Customer refund: Debit normal customer account (use UUID), Credit cash
          if (returnItem.new_customer_id) {
            financialTransactionValues.push(`(
              '${financialTransactionId}',
              '${returnItem.return_date}',
              'ارجاع للمخزن ${returnItem.return_id} - رد نقدي للعميل',
              '${returnItem.new_customer_id}',
              '1111',
              ${totalAmount},
              '${returnItem.return_id}',
              'CUSTOMER_REFUND',
              'migration',
              '${new Date().toISOString()}'
            )`);
          } else {
            // Customer not found, treat as cash return
            financialTransactionValues.push(`(
              '${financialTransactionId}',
              '${returnItem.return_date}',
              'ارجاع للمخزن ${returnItem.return_id} - رد نقدي (عميل غير موجود)',
              '4210',
              '1111',
              ${totalAmount},
              '${returnItem.return_id}',
              'INVENTORY_RETURN',
              'migration',
              '${new Date().toISOString()}'
            )`);
          }
        } else {
          // Customer advance payment: Debit returns account, Credit advance customer account
          if (returnItem.new_customer_id) {
            financialTransactionValues.push(`(
              '${financialTransactionId}',
              '${returnItem.return_date}',
              'ارجاع للمخزن ${returnItem.return_id} - دفعة مقدمة للعميل',
              '4210',
              'adv-${returnItem.new_customer_id}',
              ${totalAmount},
              '${returnItem.return_id}',
              'CUSTOMER_ADVANCE',
              'migration',
              '${new Date().toISOString()}'
            )`);
          } else {
            // Customer not found, treat as cash return
            financialTransactionValues.push(`(
              '${financialTransactionId}',
              '${returnItem.return_date}',
              'ارجاع للمخزن ${returnItem.return_id} - نقدي (عميل غير موجود)',
              '4210',
              '1111',
              ${totalAmount},
              '${returnItem.return_id}',
              'INVENTORY_RETURN',
              'migration',
              '${new Date().toISOString()}'
            )`);
          }
        }
      }
    }

    // Insert financial transactions
    if (financialTransactionValues.length > 0) {
      await run(db, `
        INSERT INTO Transactions (
          TransactionID, TransactionDate, Description,
          DebitAccountID, CreditAccountID, Amount,
          ReferenceID, ReferenceType, CreatedBy, CreatedAt
        ) VALUES ${financialTransactionValues.join(',')}
      `);
    }

    totalProcessed += returns.length;
    console.log(`Processed ${totalProcessed}/${totalReturns} returns (${Math.round(totalProcessed/totalReturns*100)}%).`);
  }

  console.log(`Created financial transactions for ${totalProcessed} inventory returns.`);
}

/**
 * Fix items with null purchase prices by adding cost adjustment transactions
 * @param {object} db - Database connection
 * @param {number} batchSize - Number of records to process in each batch
 */
async function fixNullPurchasePrices(db, batchSize = 1000) {
  console.log('Fixing items with null purchase prices...');

  // Get items with null purchase prices that have inventory transactions
  const countResult = await get(db, `
    SELECT COUNT(DISTINCT i.ItemID) as count
    FROM Items i
    JOIN old_db.items old ON i.ItemNumber = old.id
    WHERE old.purchase_price IS NULL
      AND EXISTS (
        SELECT 1 FROM InventoryTransactions it
        WHERE it.ItemID = i.ItemID
      )
  `);
  const totalItems = countResult.count;
  console.log(`Found ${totalItems} items with null purchase prices to fix.`);

  if (totalItems === 0) {
    console.log('No items with null purchase prices to fix.');
    return;
  }

  // Process in batches
  const batches = Math.ceil(totalItems / batchSize);
  let totalProcessed = 0;

  for (let batch = 0; batch < batches; batch++) {
    const offset = batch * batchSize;
    console.log(`Processing batch ${batch + 1}/${batches} (offset: ${offset})...`);

    // Get items with null purchase prices and calculate average cost from transactions
    const items = await all(db, `
      SELECT
        i.ItemID,
        i.ItemNumber,
        i.Name,
        AVG(CASE WHEN it.UnitCost > 0 THEN it.UnitCost ELSE NULL END) as avg_cost,
        COUNT(it.TransactionID) as transaction_count
      FROM Items i
      JOIN old_db.items old ON i.ItemNumber = old.id
      LEFT JOIN InventoryTransactions it ON i.ItemID = it.ItemID AND it.UnitCost > 0
      WHERE old.purchase_price IS NULL
        AND EXISTS (
          SELECT 1 FROM InventoryTransactions it2
          WHERE it2.ItemID = i.ItemID
        )
      GROUP BY i.ItemID, i.ItemNumber, i.Name
      ORDER BY i.ItemNumber
      LIMIT ${batchSize} OFFSET ${offset}
    `);

    if (items.length === 0) continue;

    let costAdjustmentValues = [];

    for (const item of items) {
      // Use average cost if available, otherwise use a default minimal cost
      const estimatedCost = item.avg_cost || 0.01;

      if (estimatedCost > 0) {
        const transactionId = uuidv4();

        costAdjustmentValues.push(`(
          '${transactionId}',
          '${item.ItemID}',
          'COST_ADJUSTMENT',
          0,
          ${estimatedCost},
          '${new Date().toISOString()}',
          'تعديل تكلفة: تقدير التكلفة للصنف ${item.Name} (متوسط: ${estimatedCost.toFixed(4)}, معاملات: ${item.transaction_count})',
          'migration'
        )`);
      }
    }

    // Insert cost adjustment transactions
    if (costAdjustmentValues.length > 0) {
      await run(db, `
        INSERT INTO InventoryTransactions (
          TransactionID, ItemID, ReferenceType, QuantityChange,
          UnitCost, TransactionDate, Note, PerformedBy
        ) VALUES ${costAdjustmentValues.join(',')}
      `);
    }

    totalProcessed += items.length;
    console.log(`Processed ${totalProcessed}/${totalItems} items (${Math.round(totalProcessed/totalItems*100)}%).`);
  }

  console.log(`Created cost adjustment transactions for ${totalProcessed} items.`);
}

/**
 * Main function to migrate inventory returns
 * @param {object} db - Database connection
 * @param {number} startTime - Start time for performance measurement
 * @param {Function} callback - Callback function to call when done
 */
async function migrateInventoryReturnsMain(db, startTime, callback) {
  try {
    console.log('Starting inventory returns migration...');

    // Migrate inventory return details to inventory transactions
    await migrateInventoryReturnDetails(db);

    // Migrate inventory return headers to financial transactions
    await migrateInventoryReturnHeaders(db);

    // Fix null purchase prices
    await fixNullPurchasePrices(db);

    const endTime = performance.now();
    console.log(`Inventory returns migration completed in ${((endTime - startTime) / 1000).toFixed(2)} seconds.`);

    callback(null);
  } catch (err) {
    console.error(`Error migrating inventory returns: ${err.message}`);
    callback(err);
  }
}

module.exports = { migrateInventoryReturnsMain };
